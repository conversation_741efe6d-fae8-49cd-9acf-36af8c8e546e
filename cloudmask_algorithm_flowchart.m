function cloudmask_algorithm_flowchart()
% CloudMask云识别算法流程图生成器
% 生成详细的算法流程图和解释说明文档

clc; clear; close all;

%% 设置输出目录
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\cloudmask_algorithm';
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% 生成流程图
generate_cloudmask_flowchart(outputDir);

%% 生成详细说明文档
generate_algorithm_documentation(outputDir);

fprintf('CloudMask算法流程图和说明文档生成完成！\n');
fprintf('结果保存在: %s\n', outputDir);

end

%% -------- 子函数：生成CloudMask流程图 --------
function generate_cloudmask_flowchart(outputDir)
    fprintf('生成CloudMask算法流程图...\n');
    
    % 创建大图
    fig = figure('Position', [100, 100, 1400, 1000]);
    
    % 设置坐标轴
    ax = axes('Position', [0.05, 0.05, 0.9, 0.9]);
    xlim([0, 10]);
    ylim([0, 12]);
    axis off;
    
    % 定义颜色
    colors = struct();
    colors.input = [0.8, 0.9, 1.0];      % 浅蓝色 - 输入
    colors.process = [0.9, 0.9, 0.8];    % 浅黄色 - 处理
    colors.decision = [1.0, 0.8, 0.8];   % 浅红色 - 判断
    colors.output = [0.8, 1.0, 0.8];     % 浅绿色 - 输出
    
    % 绘制流程图元素
    
    % 1. 输入数据
    draw_box(ax, 4.5, 11, 3, 0.6, '输入: 激光雷达后向散射数据\nBackscatter(高度, 时间)', colors.input);
    
    % 2. 数据预处理
    draw_arrow(ax, 4.5, 10.7, 4.5, 10.1);
    draw_box(ax, 4.5, 9.8, 3, 0.6, '数据预处理:\n距离校正 P = Backscatter / height²', colors.process);
    
    % 3. 噪声计算
    draw_arrow(ax, 4.5, 9.5, 4.5, 8.9);
    draw_box(ax, 4.5, 8.6, 3, 0.6, '噪声水平计算:\nsd = std(P[高度>15km])\nnoise = k × sd', colors.process);
    
    % 4. 信号平滑
    draw_arrow(ax, 4.5, 8.3, 4.5, 7.7);
    draw_box(ax, 4.5, 7.4, 3, 0.6, '信号平滑:\nPs = movmean(P, 3)', colors.process);
    
    % 5. 前向后向处理
    draw_arrow(ax, 4.5, 7.1, 4.5, 6.5);
    draw_box(ax, 1.5, 6.2, 2.5, 0.8, '前向扫描:\nPD1处理\n保持信号连续性', colors.process);
    draw_box(ax, 6.5, 6.2, 2.5, 0.8, '后向扫描:\nPD2处理\n保持信号连续性', colors.process);
    
    % 连接线
    draw_arrow(ax, 3.5, 6.6, 2.75, 6.6);
    draw_arrow(ax, 5.5, 6.6, 7.25, 6.6);
    
    % 6. 平均处理
    draw_arrow(ax, 2.75, 5.8, 4.5, 5.2);
    draw_arrow(ax, 7.25, 5.8, 4.5, 5.2);
    draw_box(ax, 4.5, 4.9, 3, 0.6, '平均处理:\nPD = (PD1 + PD2) / 2', colors.process);
    
    % 7. 层检测算法
    draw_arrow(ax, 4.5, 4.6, 4.5, 4.0);
    draw_box(ax, 4.5, 3.7, 3, 0.6, '层检测算法:\n基于排序和基线比较\n识别层边界', colors.process);
    
    % 8. 云层判断
    draw_arrow(ax, 4.5, 3.4, 4.5, 2.8);
    draw_diamond(ax, 4.5, 2.5, 2, 0.8, 'F > 800 或\nF2 > 16?', colors.decision);
    
    % 9. 水云检测
    draw_arrow(ax, 3.5, 2.5, 1.5, 2.5);
    draw_diamond(ax, 1.5, 2.5, 1.8, 0.8, '高度<2km且\n后向散射>0.1?', colors.decision);
    
    % 10. 梯度检测
    draw_arrow(ax, 1.5, 2.1, 1.5, 1.5);
    draw_diamond(ax, 1.5, 1.2, 1.8, 0.8, '梯度>3或<-7?', colors.decision);
    
    % 11. 输出结果
    draw_arrow(ax, 5.5, 2.5, 7.5, 2.5);
    draw_arrow(ax, 2.4, 2.5, 7.5, 2.5);
    draw_arrow(ax, 2.4, 1.2, 7.5, 1.2);
    draw_arrow(ax, 7.5, 1.2, 7.5, 2.1);
    draw_box(ax, 7.5, 1.8, 2, 0.6, '输出: 云层掩码\ncloud_mask', colors.output);
    
    % 添加标题和说明
    title('CloudMask云识别算法流程图', 'FontSize', 16, 'FontWeight', 'bold');
    
    % 添加图例
    legend_x = 0.5;
    legend_y = 0.5;
    draw_box(ax, legend_x, legend_y+0.6, 1.5, 0.3, '输入数据', colors.input);
    draw_box(ax, legend_x, legend_y+0.3, 1.5, 0.3, '处理步骤', colors.process);
    draw_box(ax, legend_x, legend_y, 1.5, 0.3, '判断条件', colors.decision);
    draw_box(ax, legend_x, legend_y-0.3, 1.5, 0.3, '输出结果', colors.output);
    
    text(legend_x-0.2, legend_y+0.9, '图例:', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 保存图像
    outputFile = fullfile(outputDir, 'cloudmask_algorithm_flowchart.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存流程图: %s\n', outputFile);
    
    close(fig);
end

%% -------- 辅助绘图函数 --------
function draw_box(ax, x, y, width, height, text_str, color)
    % 绘制矩形框
    rectangle('Position', [x-width/2, y-height/2, width, height], ...
              'FaceColor', color, 'EdgeColor', 'black', 'LineWidth', 1.5);
    
    % 添加文本
    text(x, y, text_str, 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 9, 'FontWeight', 'bold');
end

function draw_diamond(ax, x, y, width, height, text_str, color)
    % 绘制菱形
    diamond_x = [x, x+width/2, x, x-width/2, x];
    diamond_y = [y+height/2, y, y-height/2, y, y+height/2];
    
    patch(diamond_x, diamond_y, color, 'EdgeColor', 'black', 'LineWidth', 1.5);
    
    % 添加文本
    text(x, y, text_str, 'HorizontalAlignment', 'center', ...
         'VerticalAlignment', 'middle', 'FontSize', 8, 'FontWeight', 'bold');
end

function draw_arrow(ax, x1, y1, x2, y2)
    % 绘制箭头
    annotation('arrow', [x1/10, x2/10], [y1/12, y2/12], ...
               'HeadStyle', 'cback1', 'HeadLength', 8, 'HeadWidth', 8, ...
               'LineWidth', 1.5, 'Color', 'black');
end

%% -------- 子函数：生成算法说明文档 --------
function generate_algorithm_documentation(outputDir)
    fprintf('生成CloudMask算法详细说明文档...\n');

    % 创建说明文档
    docFile = fullfile(outputDir, 'CloudMask_Algorithm_Documentation.txt');

    fid = fopen(docFile, 'w');
    if fid == -1
        fprintf('错误: 无法创建说明文档\n');
        return;
    end

    try
        % 写入文档头部
        fprintf(fid, '========================================\n');
        fprintf(fid, 'CloudMask云识别算法详细说明文档\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '生成时间: %s\n', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
        fprintf(fid, '版本: V4 (正确CloudMask实现)\n\n');

        % 1. 算法概述
        fprintf(fid, '1. 算法概述\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, 'CloudMask算法是一种基于激光雷达后向散射数据的云层自动识别算法。\n');
        fprintf(fid, '该算法通过分析后向散射信号的空间分布特征，识别大气中的云层区域。\n\n');

        fprintf(fid, '主要特点:\n');
        fprintf(fid, '- 基于层检测的云识别方法\n');
        fprintf(fid, '- 多重判断条件确保识别准确性\n');
        fprintf(fid, '- 适用于不同类型的云层（水云、冰云等）\n');
        fprintf(fid, '- 自适应噪声处理\n\n');

        % 2. 算法详细步骤
        fprintf(fid, '2. 算法详细步骤\n');
        fprintf(fid, '========================================\n\n');

        fprintf(fid, '步骤1: 数据输入和预处理\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '输入数据: 激光雷达后向散射系数 Backscatter(高度, 时间)\n');
        fprintf(fid, '距离校正: P = Backscatter / (height^2)\n');
        fprintf(fid, '目的: 消除距离对信号强度的影响\n\n');

        fprintf(fid, '步骤2: 噪声水平估算\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '选择高度 > 15km 的数据作为噪声参考\n');
        fprintf(fid, '计算标准差: sd = std(P[height > 15km])\n');
        fprintf(fid, '噪声阈值: noise = k × sd (k=6)\n');
        fprintf(fid, '目的: 确定信号变化的显著性阈值\n\n');

        fprintf(fid, '步骤3: 信号平滑处理\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '移动平均: Ps = movmean(P, 3)\n');
        fprintf(fid, '目的: 减少随机噪声，保持信号主要特征\n\n');

        fprintf(fid, '步骤4: 前向和后向扫描处理\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '前向扫描 (PD1):\n');
        fprintf(fid, '  for zi = 1:(Nz-1)\n');
        fprintf(fid, '    if abs(PD1(zi+1) - PD1(zi)) < noise\n');
        fprintf(fid, '      PD1(zi+1) = PD1(zi)  % 保持连续性\n');
        fprintf(fid, '    end\n');
        fprintf(fid, '  end\n\n');

        fprintf(fid, '后向扫描 (PD2):\n');
        fprintf(fid, '  for zi = 1:(Nz-1)\n');
        fprintf(fid, '    if abs(PD2(Nz-zi) - PD2(Nz-zi+1)) < noise\n');
        fprintf(fid, '      PD2(Nz-zi) = PD2(Nz-zi+1)  % 保持连续性\n');
        fprintf(fid, '    end\n');
        fprintf(fid, '  end\n\n');

        fprintf(fid, '平均处理: PD = (PD1 + PD2) / 2\n');
        fprintf(fid, '目的: 通过双向扫描增强信号的连续性和稳定性\n\n');

        fprintf(fid, '步骤5: 层检测算法\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '5.1 信号排序和基线计算:\n');
        fprintf(fid, '   [Rs, Is] = sort(PD)  % 按信号强度排序\n');
        fprintf(fid, '   MA = max(Rs), MI = min(Rs)\n');
        fprintf(fid, '   PE = (1:Nz) / Nz  % 累积概率\n');
        fprintf(fid, '   y = PE × (MA - MI) + MI\n');
        fprintf(fid, '   PN(Is) = y  % 重新排列\n');
        fprintf(fid, '   B = ((Nz:-1:1) / Nz) × (MA - MI) + MI  % 基线\n\n');

        fprintf(fid, '5.2 层边界检测:\n');
        fprintf(fid, '   寻找 PN(zi+1) > B(zi+1) 且 PN(zi) <= B(zi) 的位置\n');
        fprintf(fid, '   计算层的面积: Area = sum(PN - B) for layer region\n');
        fprintf(fid, '   记录层的底部(base)和顶部(top)高度\n\n');

        fprintf(fid, '步骤6: 云层判断条件\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '6.1 主要判断条件 (F/F2阈值):\n');
        fprintf(fid, '   F = Area × PMX / (height_diff) / MA\n');
        fprintf(fid, '   F2 = max(F1[base:top])  % F1 = 100×(PN-B)/MA\n');
        fprintf(fid, '   if F > 800 or F2 > 16:\n');
        fprintf(fid, '     识别为云层\n\n');

        fprintf(fid, '6.2 水云检测条件:\n');
        fprintf(fid, '   if height < 2km and median(PM) > 0.1 and prctile(PM,90) > 1:\n');
        fprintf(fid, '     if F > 1:\n');
        fprintf(fid, '       识别为水云\n\n');

        fprintf(fid, '6.3 梯度检测条件:\n');
        fprintf(fid, '   计算对数NRB梯度: G = d(ln(PN×height²))/dz\n');
        fprintf(fid, '   if max(G) > 3 or min(G) < -7:\n');
        fprintf(fid, '     识别为云层\n\n');

        fprintf(fid, '步骤7: 输出结果\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '生成云层掩码矩阵: cloud_mask(高度, 时间)\n');
        fprintf(fid, '1 = 云层区域, 0 = 非云层区域\n\n');

        % 3. 算法参数说明
        fprintf(fid, '3. 算法参数说明\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, 'F_threshold = 800     % 主要云层判断阈值\n');
        fprintf(fid, 'F2_threshold = 16     % 辅助云层判断阈值\n');
        fprintf(fid, 'k = 6                 % 噪声系数\n');
        fprintf(fid, 'maxHeight = 15.0      % 最大处理高度 (km)\n');
        fprintf(fid, 'water_cloud_threshold = 0.1  % 水云后向散射阈值\n');
        fprintf(fid, 'gradient_high = 3     % 梯度上限\n');
        fprintf(fid, 'gradient_low = -7     % 梯度下限\n\n');

        % 4. 算法优势和特点
        fprintf(fid, '4. 算法优势和特点\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '4.1 多重判断机制:\n');
        fprintf(fid, '   - F/F2阈值: 识别一般云层\n');
        fprintf(fid, '   - 水云条件: 专门识别低层水云\n');
        fprintf(fid, '   - 梯度条件: 识别边界清晰的云层\n\n');

        fprintf(fid, '4.2 自适应噪声处理:\n');
        fprintf(fid, '   - 基于实际数据计算噪声水平\n');
        fprintf(fid, '   - 避免固定阈值的局限性\n\n');

        fprintf(fid, '4.3 双向扫描技术:\n');
        fprintf(fid, '   - 前向和后向扫描结合\n');
        fprintf(fid, '   - 增强信号连续性和稳定性\n\n');

        fprintf(fid, '4.4 层检测方法:\n');
        fprintf(fid, '   - 基于信号分布统计特征\n');
        fprintf(fid, '   - 能够识别复杂的多层云结构\n\n');

        % 5. 应用场景和限制
        fprintf(fid, '5. 应用场景和限制\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '5.1 适用场景:\n');
        fprintf(fid, '   - 激光雷达云层自动识别\n');
        fprintf(fid, '   - 大气垂直结构分析\n');
        fprintf(fid, '   - 气象观测数据处理\n');
        fprintf(fid, '   - 过冷水识别的前处理步骤\n\n');

        fprintf(fid, '5.2 算法限制:\n');
        fprintf(fid, '   - 需要高质量的激光雷达数据\n');
        fprintf(fid, '   - 对强降水情况的识别可能受限\n');
        fprintf(fid, '   - 参数需要根据具体设备调整\n\n');

        % 6. 实际应用效果
        fprintf(fid, '6. 实际应用效果\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '在过冷水识别项目中的应用效果:\n');
        fprintf(fid, '- 云覆盖率识别范围: 5.2%% - 14.9%%\n');
        fprintf(fid, '- 主要通过F/F2阈值检测识别云层\n');
        fprintf(fid, '- 水云和梯度检测作为补充手段\n');
        fprintf(fid, '- 为过冷水识别提供了可靠的云层基础\n\n');

        fprintf(fid, '========================================\n');
        fprintf(fid, '文档结束\n');
        fprintf(fid, '========================================\n');

        fclose(fid);
        fprintf('保存算法说明文档: %s\n', docFile);

    catch ME
        fclose(fid);
        fprintf('生成文档时出错: %s\n', ME.message);
    end
end
