function test_depol_01_005()
% 测试Depol阈值0.1和0.05的过冷水识别效果
% 基于优化的CloudMask算法和温度约束

clc; clear; close all;

%% -------- 设置路径 --------
lidarDir = 'D:\lidar\supercooledwaterday-hourly';
backscatterDir = 'D:\lidar\backscatter';
era5Dir = 'D:\lidar\supercooledwaterday-hourly\era5';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\depol_01_005_test';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 参数设置 --------
% 过冷水温度范围
temp_max = 0;    % 最高温度 0°C
temp_min = -40;  % 最低温度 -40°C

% 测试的Depol阈值
depol_test_values = [0.05, 0.1];

% 固定的BackScatter阈值（基于之前的优化结果）
backscatter_threshold = 0.04;

% CloudMask参数
cloudmask_params = struct();
cloudmask_params.F_threshold = 800;
cloudmask_params.F2_threshold = 16;
cloudmask_params.maxHeight = 15.0;
cloudmask_params.k = 6;

fprintf('Depol阈值测试参数 (0.1 vs 0.05):\n');
fprintf('  测试Depol阈值: [%.2f, %.2f]\n', depol_test_values);
fprintf('  固定BackScatter阈值: %.2f\n', backscatter_threshold);
fprintf('  过冷水温度范围: %.1f°C 到 %.1f°C\n', temp_min, temp_max);

%% -------- 查找数据文件 --------
depolFiles = dir(fullfile(lidarDir, 'Depol_*.csv'));
fprintf('\n找到 %d 个Depol文件\n', length(depolFiles));

if isempty(depolFiles)
    error('未找到Depol文件，请检查数据路径');
end

% 选择几个代表性的文件进行测试
test_files = min(5, length(depolFiles));  % 测试前5个文件
fprintf('将测试前 %d 个文件\n', test_files);

%% -------- 处理文件 --------
all_results = [];

for fileIdx = 1:test_files
    try
        % 获取文件信息
        depolFile = depolFiles(fileIdx).name;
        fprintf('\n=== 处理文件 %d/%d: %s ===\n', fileIdx, test_files, depolFile);
        
        % 从文件名提取日期
        dateMatch = regexp(depolFile, 'Depol_(\d{8})', 'tokens');
        if isempty(dateMatch)
            fprintf('  警告: 无法从文件名解析日期，跳过\n');
            continue;
        end
        dateStr = dateMatch{1}{1};
        dateFormatted = [dateStr(1:4), '-', dateStr(5:6), '-', dateStr(7:8)];
        
        % 查找对应文件
        backscatterFile = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', dateStr));
        backscatterFiles = dir(backscatterFile);
        if isempty(backscatterFiles)
            fprintf('  警告: 找不到对应的BackScatter文件，跳过\n');
            continue;
        end
        
        era5File = fullfile(era5Dir, sprintf('era5_temp_geopotential_%s.nc', dateStr));
        if ~exist(era5File, 'file')
            fprintf('  警告: 找不到对应的ERA5文件，跳过\n');
            continue;
        end
        
        % 加载数据
        [lidar_data, success] = load_lidar_data_test(lidarDir, backscatterDir, depolFile, backscatterFiles(1).name);
        if ~success
            fprintf('  错误: 激光雷达数据加载失败\n');
            continue;
        end
        
        [era5_data, success] = load_era5_data_test(era5File);
        if ~success
            fprintf('  错误: ERA5数据加载失败\n');
            continue;
        end
        
        % CloudMask云识别（简化版本）
        [cloud_mask, cloud_stats] = identify_clouds_simple(lidar_data);
        fprintf('  CloudMask云识别完成，云覆盖率: %.1f%%\n', sum(cloud_mask(:))/numel(cloud_mask)*100);
        
        % 温度约束
        [temp_mask, height_info] = apply_temperature_constraint_test(lidar_data, era5_data, temp_min, temp_max);
        fprintf('  温度约束完成，过冷区域覆盖率: %.1f%%\n', sum(temp_mask(:))/numel(temp_mask)*100);
        
        % 综合约束条件
        base_mask = cloud_mask & temp_mask;
        fprintf('  综合约束后覆盖率: %.1f%%\n', sum(base_mask(:))/numel(base_mask)*100);
        
        % 测试不同Depol阈值
        depol_results = test_depol_thresholds(lidar_data, base_mask, depol_test_values, backscatter_threshold);
        
        % 为每一天创建单独的文件夹
        dayOutputDir = fullfile(outputDir, dateFormatted);
        if ~exist(dayOutputDir, 'dir')
            mkdir(dayOutputDir);
        end
        
        % 生成对比图（只显示识别到的过冷水区域）
        generate_depol_comparison_plots(lidar_data, base_mask, height_info, depol_results, dateFormatted, dayOutputDir, backscatter_threshold);
        
        % 生成参数报告
        generate_depol_report(lidar_data, era5_data, depol_results, height_info, dateFormatted, dayOutputDir, backscatter_threshold);
        
        % 保存结果
        file_result = struct();
        file_result.date = dateFormatted;
        file_result.height_info = height_info;
        file_result.cloud_coverage = sum(cloud_mask(:))/numel(cloud_mask)*100;
        file_result.temp_coverage = sum(temp_mask(:))/numel(temp_mask)*100;
        file_result.base_coverage = sum(base_mask(:))/numel(base_mask)*100;
        file_result.depol_results = depol_results;
        
        all_results = [all_results; file_result];
        
        fprintf('  处理完成: %s\n', dateFormatted);
        
    catch ME
        fprintf('  处理文件 %s 时出错: %s\n', depolFile, ME.message);
        continue;
    end
end

%% -------- 生成汇总分析 --------
if ~isempty(all_results)
    generate_summary_analysis(all_results, depol_test_values, outputDir, backscatter_threshold);
    fprintf('\n所有文件处理完成！\n');
    fprintf('结果保存在: %s\n', outputDir);
else
    fprintf('\n没有成功处理的文件\n');
end

end

%% -------- 子函数：测试不同Depol阈值 --------
function depol_results = test_depol_thresholds(lidar_data, base_mask, depol_values, backscatter_threshold)
    fprintf('    测试Depol阈值 0.1 vs 0.05 的效果...\n');
    
    depol_results = struct();
    depol_results.thresholds = depol_values;
    depol_results.detection_counts = zeros(size(depol_values));
    depol_results.coverage_rates = zeros(size(depol_values));
    
    base_count = sum(base_mask(:));
    
    for i = 1:length(depol_values)
        depol_thresh = depol_values(i);
        
        % 应用阈值条件
        depol_condition = lidar_data.depol < depol_thresh;
        backscatter_condition = lidar_data.backscatter > backscatter_threshold;
        
        % 综合所有条件
        supercooled_mask = base_mask & depol_condition & backscatter_condition;
        
        % 计算识别像素数和覆盖率
        detection_count = sum(supercooled_mask(:));
        if base_count > 0
            coverage_rate = detection_count / base_count * 100;
        else
            coverage_rate = 0;
        end
        
        depol_results.detection_counts(i) = detection_count;
        depol_results.coverage_rates(i) = coverage_rate;
        
        fprintf('      Depol < %.2f: 检测 %d 像素, 覆盖率 %.1f%%\n', ...
                depol_thresh, detection_count, coverage_rate);
    end
    
    % 计算差异
    if length(depol_values) == 2
        diff_count = depol_results.detection_counts(2) - depol_results.detection_counts(1);
        diff_rate = depol_results.coverage_rates(2) - depol_results.coverage_rates(1);
        fprintf('      差异 (0.1 vs 0.05): +%d 像素, +%.1f%%\n', diff_count, diff_rate);
    end
end

%% -------- 子函数：加载激光雷达数据 --------
function [lidar_data, success] = load_lidar_data_test(lidarDir, backscatterDir, depolFile, backscatterFile)
    success = false;
    lidar_data = struct();

    try
        % 加载数据
        depolPath = fullfile(lidarDir, depolFile);
        raw_depol = readmatrix(depolPath);

        backscatterPath = fullfile(backscatterDir, backscatterFile);
        raw_backscatter = readmatrix(backscatterPath);

        % 处理高度数据
        height = raw_depol(:,1);
        valid_idx = ~isnan(height);
        height = height(valid_idx);

        % 限制高度范围到4km以内
        height_mask = height <= 4000;
        height = height(height_mask);

        % 获取24小时数据
        max_time_points = 1440;
        depol_data = raw_depol(valid_idx, 2:min(end, max_time_points+1));
        depol_data = depol_data(height_mask, :);

        backscatter_data = raw_backscatter(valid_idx, 2:min(end, max_time_points+1));
        backscatter_data = backscatter_data(height_mask, :);

        % 创建时间向量
        num_times = size(depol_data, 2);
        time_vector = 0:(num_times-1);

        % 存储数据
        lidar_data.height = height;
        lidar_data.time = time_vector;
        lidar_data.depol = double(depol_data);
        lidar_data.backscatter = double(backscatter_data);

        fprintf('    激光雷达数据加载成功 (高度: %.0f-%.0f m, 时间: %d 分钟)\n', ...
                min(height), max(height), num_times);

        success = true;

    catch ME
        fprintf('    激光雷达数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：加载ERA5数据 --------
function [era5_data, success] = load_era5_data_test(era5File)
    success = false;
    era5_data = struct();

    try
        % 读取ERA5数据
        temperature = ncread(era5File, 't');
        geopotential = ncread(era5File, 'z');
        pressure_levels = ncread(era5File, 'pressure_level');
        time = ncread(era5File, 'valid_time');

        % 转换单位
        temperature_celsius = squeeze(temperature) - 273.15;
        geopotential_height = squeeze(geopotential) / 9.80665;

        % 存储数据
        era5_data.temperature = temperature_celsius;
        era5_data.height = geopotential_height;
        era5_data.pressure = pressure_levels;
        era5_data.time = time;

        fprintf('    ERA5数据加载成功\n');
        success = true;

    catch ME
        fprintf('    ERA5数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：简化的云识别 --------
function [cloud_mask, cloud_stats] = identify_clouds_simple(lidar_data)
    % 简化版本，使用后向散射阈值识别云层
    fprintf('    应用简化云识别算法...\n');

    % 使用后向散射阈值来识别云层
    cloud_threshold = 1e-5;  % 后向散射阈值
    cloud_mask = lidar_data.backscatter > cloud_threshold;

    % 统计信息
    cloud_stats = struct();
    cloud_stats.threshold_detections = sum(cloud_mask(:));
    cloud_stats.water_cloud_detections = 0;
    cloud_stats.gradient_detections = 0;
end

%% -------- 子函数：温度约束 --------
function [temp_mask, height_info] = apply_temperature_constraint_test(lidar_data, era5_data, temp_min, temp_max)
    fprintf('    应用过冷水温度约束...\n');

    % 初始化温度掩码
    temp_mask = false(size(lidar_data.depol));

    % 计算平均温度剖面
    mean_temp_profile = mean(era5_data.temperature, 2);
    mean_height_profile = mean(era5_data.height, 2);

    % 插值到激光雷达高度网格
    temp_interp_mean = interp1(mean_height_profile, mean_temp_profile, lidar_data.height, 'linear', 'extrap');

    % 找到过冷水温度范围对应的高度范围
    supercooled_indices = find((temp_interp_mean >= temp_min) & (temp_interp_mean <= temp_max));

    if ~isempty(supercooled_indices)
        supercooled_height_min = lidar_data.height(min(supercooled_indices));
        supercooled_height_max = lidar_data.height(max(supercooled_indices));

        % 找到0°C和-40°C对应的具体高度
        [~, zero_idx] = min(abs(temp_interp_mean - 0));
        [~, minus40_idx] = min(abs(temp_interp_mean - (-40)));

        zero_height = lidar_data.height(zero_idx);
        minus40_height = lidar_data.height(minus40_idx);

        height_info = struct();
        height_info.supercooled_range = [supercooled_height_min, supercooled_height_max];
        height_info.zero_height = zero_height;
        height_info.minus40_height = minus40_height;

        fprintf('    过冷水高度范围: %.0f - %.0f m\n', supercooled_height_min, supercooled_height_max);
    else
        height_info = struct();
        height_info.supercooled_range = [NaN, NaN];
        height_info.zero_height = NaN;
        height_info.minus40_height = NaN;

        fprintf('    警告: 未找到过冷水温度区域\n');
    end

    % 对每个时间点应用温度约束
    for t = 1:length(lidar_data.time)
        era5_time_idx = min(t, size(era5_data.temperature, 2));
        temp_profile = era5_data.temperature(:, era5_time_idx);
        height_profile = era5_data.height(:, era5_time_idx);
        temp_interp = interp1(height_profile, temp_profile, lidar_data.height, 'linear', 'extrap');
        temp_condition = (temp_interp >= temp_min) & (temp_interp <= temp_max);
        temp_mask(:, t) = temp_condition;
    end
end

%% -------- 子函数：生成Depol对比图（只显示识别区域）--------
function generate_depol_comparison_plots(lidar_data, base_mask, height_info, depol_results, dateStr, outputDir, backscatter_threshold)
    fprintf('    生成Depol阈值对比图（只显示识别区域）...\n');

    % 时间轴（转换为小时）
    time_hours = lidar_data.time / 60;
    depol_values = depol_results.thresholds;

    % 为每个Depol阈值生成单独的图像
    for i = 1:length(depol_values)
        % 创建图形 - 加长x轴
        fig = figure('Position', [100, 100, 1600, 800]);

        % 计算当前Depol阈值的过冷水识别结果
        depol_condition = lidar_data.depol < depol_values(i);
        backscatter_condition = lidar_data.backscatter > backscatter_threshold;
        supercooled_mask = base_mask & depol_condition & backscatter_condition;

        % 创建显示矩阵：只显示识别到的过冷水区域
        display_matrix = zeros(size(supercooled_mask));
        display_matrix(supercooled_mask) = 1;  % 过冷水区域设为1

        % 绘制过冷水识别结果
        pcolor(time_hours, lidar_data.height/1000, display_matrix);
        shading flat;

        % 设置颜色映射：白色背景，深蓝色表示过冷水
        colormap([1 1 1; 0 0 0.8]);  % 白色背景，深蓝色过冷水

        % 设置坐标轴
        xlabel('时间 (小时)', 'FontSize', 12, 'FontWeight', 'bold');
        ylabel('高度 (km)', 'FontSize', 12, 'FontWeight', 'bold');

        % 设置x轴范围为24小时
        xlim([0, 24]);
        ylim([0, 4]);

        % 设置网格
        grid on;
        set(gca, 'GridAlpha', 0.3);

        % 添加标题
        if ~isnan(height_info.supercooled_range(1))
            title_str = sprintf('过冷水识别结果 - %s\n过冷水高度: %.0f-%.0f m | Depol<%.2f, BackScatter>%.2f | 覆盖率: %.1f%%', ...
                               dateStr, height_info.supercooled_range(1), height_info.supercooled_range(2), ...
                               depol_values(i), backscatter_threshold, depol_results.coverage_rates(i));
        else
            title_str = sprintf('过冷水识别结果 - %s\nDepol<%.2f, BackScatter>%.2f | 覆盖率: %.1f%%', ...
                               dateStr, depol_values(i), backscatter_threshold, depol_results.coverage_rates(i));
        end
        title(title_str, 'FontSize', 14, 'FontWeight', 'bold');

        % 添加颜色条
        cb = colorbar;
        cb.Ticks = [0, 1];
        cb.TickLabels = {'无过冷水', '过冷水'};
        cb.Label.String = '识别结果';
        cb.Label.FontSize = 12;
        cb.Label.FontWeight = 'bold';

        % 添加统计信息文本框
        stats_text = {
            sprintf('识别统计:');
            sprintf('总像素数: %d', numel(supercooled_mask));
            sprintf('过冷水像素: %d', sum(supercooled_mask(:)));
            sprintf('覆盖率: %.2f%%', depol_results.coverage_rates(i));
            '';
            sprintf('参数设置:');
            sprintf('Depol阈值: < %.2f', depol_values(i));
            sprintf('BackScatter阈值: > %.2f', backscatter_threshold);
            sprintf('温度范围: -40°C ~ 0°C');
        };

        % 在图的右上角添加统计信息
        annotation('textbox', [0.75, 0.75, 0.2, 0.2], 'String', stats_text, ...
                   'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
                   'FitBoxToText', 'on');

        % 保存图像
        outputFile = fullfile(outputDir, sprintf('supercooled_water_depol_%s_%s.png', ...
                                                 strrep(num2str(depol_values(i)), '.', ''), strrep(dateStr, '-', '')));
        print(fig, outputFile, '-dpng', '-r300');
        fprintf('    保存图像: %s\n', outputFile);

        close(fig);
    end

    % 生成对比图
    generate_comparison_chart(depol_results, dateStr, outputDir);
end

%% -------- 子函数：生成对比图表 --------
function generate_comparison_chart(depol_results, dateStr, outputDir)
    % 创建对比图表
    fig = figure('Position', [100, 100, 800, 600]);

    % 柱状图对比
    bar(depol_results.thresholds, depol_results.coverage_rates, 'FaceColor', [0.3 0.6 0.9]);
    xlabel('Depol阈值', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('覆盖率 (%)', 'FontSize', 12, 'FontWeight', 'bold');
    title(sprintf('Depol阈值对比 - %s', dateStr), 'FontSize', 14, 'FontWeight', 'bold');
    grid on;

    % 添加数值标签
    for i = 1:length(depol_results.thresholds)
        text(depol_results.thresholds(i), depol_results.coverage_rates(i) + max(depol_results.coverage_rates)*0.02, ...
             sprintf('%.1f%%', depol_results.coverage_rates(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 计算并显示差异
    if length(depol_results.thresholds) == 2
        diff_rate = depol_results.coverage_rates(2) - depol_results.coverage_rates(1);
        text(mean(depol_results.thresholds), max(depol_results.coverage_rates)*0.8, ...
             sprintf('差异: +%.1f%%', diff_rate), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'red', 'FontSize', 12);
    end

    % 保存对比图
    outputFile = fullfile(outputDir, sprintf('depol_comparison_%s.png', strrep(dateStr, '-', '')));
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('    保存对比图: %s\n', outputFile);

    close(fig);
end

%% -------- 子函数：生成参数报告 --------
function generate_depol_report(lidar_data, era5_data, depol_results, height_info, dateStr, outputDir, backscatter_threshold)
    fprintf('    生成Depol阈值测试报告...\n');

    % 创建报告文件
    reportFile = fullfile(outputDir, sprintf('depol_test_report_%s.txt', strrep(dateStr, '-', '')));

    fid = fopen(reportFile, 'w');
    if fid == -1
        fprintf('    错误: 无法创建报告文件\n');
        return;
    end

    try
        % 写入报告头部
        fprintf(fid, '========================================\n');
        fprintf(fid, 'Depol阈值测试报告 (0.1 vs 0.05)\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '日期: %s\n', dateStr);
        fprintf(fid, '生成时间: %s\n', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
        fprintf(fid, '测试目的: 比较Depol阈值0.1和0.05的过冷水识别效果\n\n');

        % 数据基本信息
        fprintf(fid, '1. 数据基本信息\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '激光雷达数据:\n');
        fprintf(fid, '  高度范围: %.0f - %.0f m\n', min(lidar_data.height), max(lidar_data.height));
        fprintf(fid, '  时间范围: %d 分钟 (24小时)\n', length(lidar_data.time));
        fprintf(fid, '  Depol数据范围: %.3f - %.3f\n', min(lidar_data.depol(:)), max(lidar_data.depol(:)));
        fprintf(fid, '  BackScatter数据范围: %.2e - %.2e\n', min(lidar_data.backscatter(:)), max(lidar_data.backscatter(:)));
        fprintf(fid, '\n');

        fprintf(fid, 'ERA5温度数据:\n');
        fprintf(fid, '  温度范围: %.1f - %.1f °C\n', min(era5_data.temperature(:)), max(era5_data.temperature(:)));
        fprintf(fid, '  高度范围: %.0f - %.0f m\n', min(era5_data.height(:)), max(era5_data.height(:)));
        fprintf(fid, '\n');

        % 过冷水高度信息
        fprintf(fid, '2. 过冷水高度信息\n');
        fprintf(fid, '----------------------------------------\n');
        if ~isnan(height_info.supercooled_range(1))
            fprintf(fid, '过冷水高度范围: %.0f - %.0f m\n', height_info.supercooled_range(1), height_info.supercooled_range(2));
            fprintf(fid, '0°C对应高度: %.0f m\n', height_info.zero_height);
            fprintf(fid, '-40°C对应高度: %.0f m\n', height_info.minus40_height);
            fprintf(fid, '过冷水层厚度: %.0f m\n', height_info.supercooled_range(2) - height_info.supercooled_range(1));
        else
            fprintf(fid, '过冷水高度范围: 未找到有效范围\n');
        end
        fprintf(fid, '\n');

        % 测试参数
        fprintf(fid, '3. 测试参数\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '固定BackScatter阈值: %.2f\n', backscatter_threshold);
        fprintf(fid, '测试Depol阈值: [%.2f, %.2f]\n', depol_results.thresholds);
        fprintf(fid, '温度约束: -40°C 到 0°C\n\n');

        % 测试结果
        fprintf(fid, '4. 测试结果\n');
        fprintf(fid, '----------------------------------------\n');
        for i = 1:length(depol_results.thresholds)
            fprintf(fid, 'Depol < %.2f:\n', depol_results.thresholds(i));
            fprintf(fid, '  检测像素数: %d\n', depol_results.detection_counts(i));
            fprintf(fid, '  覆盖率: %.2f%%\n', depol_results.coverage_rates(i));
            fprintf(fid, '\n');
        end

        % 对比分析
        if length(depol_results.thresholds) == 2
            diff_count = depol_results.detection_counts(2) - depol_results.detection_counts(1);
            diff_rate = depol_results.coverage_rates(2) - depol_results.coverage_rates(1);

            fprintf(fid, '5. 对比分析\n');
            fprintf(fid, '----------------------------------------\n');
            fprintf(fid, '0.1 vs 0.05 对比:\n');
            fprintf(fid, '  像素数差异: %+d (0.1相对于0.05)\n', diff_count);
            fprintf(fid, '  覆盖率差异: %+.2f%% (0.1相对于0.05)\n', diff_rate);
            fprintf(fid, '\n');

            if diff_rate > 1
                fprintf(fid, '结论: Depol < 0.1 显著优于 Depol < 0.05\n');
            elseif diff_rate > 0.5
                fprintf(fid, '结论: Depol < 0.1 适度优于 Depol < 0.05\n');
            elseif diff_rate > -0.5
                fprintf(fid, '结论: 两个阈值效果相近\n');
            else
                fprintf(fid, '结论: Depol < 0.05 优于 Depol < 0.1\n');
            end
        end

        fprintf(fid, '\n========================================\n');
        fprintf(fid, '报告结束\n');
        fprintf(fid, '========================================\n');

        fclose(fid);
        fprintf('    保存测试报告: %s\n', reportFile);

    catch ME
        fclose(fid);
        fprintf('    生成报告时出错: %s\n', ME.message);
    end
end

%% -------- 子函数：生成汇总分析 --------
function generate_summary_analysis(all_results, depol_values, outputDir, backscatter_threshold)
    fprintf('\n生成汇总分析...\n');

    % 创建汇总图
    fig = figure('Position', [100, 100, 1200, 800]);

    % 提取数据
    dates = {all_results.date};
    num_files = length(all_results);

    % 子图1: 不同日期的覆盖率对比
    subplot(2, 2, 1);
    coverage_matrix = zeros(num_files, length(depol_values));
    for i = 1:num_files
        coverage_matrix(i, :) = all_results(i).depol_results.coverage_rates;
    end

    bar(coverage_matrix);
    xlabel('日期');
    ylabel('覆盖率 (%)');
    title('不同日期的Depol阈值效果对比');
    legend(arrayfun(@(x) sprintf('Depol<%.2f', x), depol_values, 'UniformOutput', false), 'Location', 'best');
    set(gca, 'XTickLabel', dates);
    xtickangle(45);
    grid on;

    % 子图2: 平均覆盖率对比
    subplot(2, 2, 2);
    mean_coverage = mean(coverage_matrix, 1);
    std_coverage = std(coverage_matrix, 1);

    bar(depol_values, mean_coverage, 'FaceColor', [0.3 0.6 0.9]);
    hold on;
    errorbar(depol_values, mean_coverage, std_coverage, 'k.', 'LineWidth', 2);
    xlabel('Depol阈值');
    ylabel('平均覆盖率 (%)');
    title('平均效果对比');
    grid on;

    % 添加数值标签
    for i = 1:length(depol_values)
        text(depol_values(i), mean_coverage(i) + std_coverage(i) + max(mean_coverage)*0.05, ...
             sprintf('%.1f±%.1f%%', mean_coverage(i), std_coverage(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 子图3: 差异分析
    subplot(2, 2, 3);
    if length(depol_values) == 2
        differences = coverage_matrix(:, 2) - coverage_matrix(:, 1);
        bar(1:num_files, differences, 'FaceColor', [0.9 0.6 0.3]);
        xlabel('日期');
        ylabel('覆盖率差异 (%)');
        title('0.1 vs 0.05 覆盖率差异');
        set(gca, 'XTickLabel', dates);
        xtickangle(45);
        grid on;

        % 添加平均线
        mean_diff = mean(differences);
        hold on;
        plot([0.5, num_files+0.5], [mean_diff, mean_diff], 'r--', 'LineWidth', 2);
        text(num_files/2, mean_diff + max(differences)*0.1, ...
             sprintf('平均: +%.1f%%', mean_diff), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'red');
    end

    % 子图4: 统计信息
    subplot(2, 2, 4);
    axis off;

    % 创建统计文本
    stats_text = {
        '汇总统计:';
        '';
        sprintf('测试文件数: %d', num_files);
        sprintf('固定BackScatter阈值: %.2f', backscatter_threshold);
        sprintf('测试Depol阈值: [%.2f, %.2f]', depol_values);
        '';
        '平均覆盖率:';
    };

    for i = 1:length(depol_values)
        stats_text{end+1} = sprintf('  Depol < %.2f: %.1f%% (±%.1f%%)', ...
                                   depol_values(i), mean_coverage(i), std_coverage(i));
    end

    if length(depol_values) == 2
        mean_diff = mean(coverage_matrix(:, 2) - coverage_matrix(:, 1));
        stats_text = [stats_text; {
            '';
            '差异分析:';
            sprintf('  平均差异: +%.1f%%', mean_diff);
            '';
            '建议:';
        }];

        if mean_diff > 1
            stats_text{end+1} = '  推荐使用Depol < 0.1';
        elseif mean_diff > 0.5
            stats_text{end+1} = '  可考虑使用Depol < 0.1';
        else
            stats_text{end+1} = '  两个阈值效果相近';
        end
    end

    text(0.1, 0.9, stats_text, 'FontSize', 10, 'VerticalAlignment', 'top', ...
         'HorizontalAlignment', 'left', 'Units', 'normalized');

    % 添加总标题
    sgtitle('Depol阈值测试汇总分析 (0.1 vs 0.05)', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, 'depol_01_005_summary.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存汇总图像: %s\n', outputFile);

    close(fig);

    % 输出最终建议
    if length(depol_values) == 2
        mean_diff = mean(coverage_matrix(:, 2) - coverage_matrix(:, 1));
        fprintf('\n=== 最终建议 ===\n');
        fprintf('Depol阈值对比 (0.1 vs 0.05):\n');
        fprintf('  0.05阈值平均覆盖率: %.1f%% (±%.1f%%)\n', mean_coverage(1), std_coverage(1));
        fprintf('  0.1阈值平均覆盖率: %.1f%% (±%.1f%%)\n', mean_coverage(2), std_coverage(2));
        fprintf('  平均差异: +%.1f%%\n', mean_diff);

        if mean_diff > 1
            fprintf('  推荐: 使用Depol < 0.1 (显著提高覆盖率)\n');
        elseif mean_diff > 0.5
            fprintf('  推荐: 可考虑使用Depol < 0.1 (适度提高覆盖率)\n');
        else
            fprintf('  推荐: 两个阈值效果相近，可根据需要选择\n');
        end
    end
end
