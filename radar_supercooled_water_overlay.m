clc; clear; close all;

%% -------- 雷达回波与过冷水叠加显示程序 --------
% 基于lidarvsradar6picbackscatter20250818oneday.m的雷达回波叠加方法
% 将雷达回波和计算出来的过冷水进行叠加，过冷水区域用等值线进行描述

%% -------- 设置路径 --------
radarDir = 'D:\dataset\shandong';          % 雷达数据目录
lidarDir = 'D:\lidar\supercooledwaterday-hourly';    % 激光雷达数据目录
supercooledDir = 'D:\lidar\supercooledwaterday-hourly\figure\supercooled_water_v4_depol01'; % 过冷水识别结果目录
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\radar_supercooled_overlay'; % 输出目录

% 创建输出目录（如果不存在）
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 生成 RdBu 调色表 --------
base = [103 0 31;178 24 43;214 96 77;244 165 130;253 219 199; ...
        247 247 247;209 229 240;146 197 222;67 147 195;33 102 172;5 48 97]/255;
mkRdBu = @(n) flipud(interp1(1:11, base, linspace(1,11,n),'linear'));
RdBu21 = mkRdBu(21);

%% -------- 绘图参数 --------
yrange = [0 4];
figsize = [60, 27];

%% -------- 查找所有.mat文件 --------
matFiles = dir(fullfile(radarDir, '*.mat'));
fprintf('找到 %d 个雷达数据文件\n', length(matFiles));

%% -------- 批量处理文件 --------
for i = 1:length(matFiles)
    try
        % 获取当前.mat文件的完整路径
        matFilePath = fullfile(radarDir, matFiles(i).name);

        % 从文件名中提取日期 (格式为 yyyy-mm-dd_reprocessed.mat)
        [~, matFileName, ~] = fileparts(matFiles(i).name);

        % 移除 "_reprocessed" 后缀以获取日期部分
        dateStr = strrep(matFileName, '_reprocessed', '');

        % 转换为yyyymmdd格式用于匹配文件
        dateParts = split(dateStr, '-');
        if length(dateParts) == 3
            % 只处理2月的数据
            if ~strcmp(dateParts{2}, '02')
                fprintf('跳过非2月数据: %s\n', dateStr);
                continue;
            end
            yyyymmdd = strcat(dateParts{1}, dateParts{2}, dateParts{3});
        else
            fprintf('警告: 无法从文件名 %s 解析日期，跳过此文件\n', matFiles(i).name);
            continue;
        end
        
        % 查找对应的过冷水识别结果文件
        supercooledDataFile = fullfile(supercooledDir, sprintf('supercooled_data_%s.mat', yyyymmdd));
        if ~exist(supercooledDataFile, 'file')
            fprintf('警告: 找不到过冷水识别结果文件 %s，跳过\n', supercooledDataFile);
            continue;
        end
        
        % 加载雷达数据
        fprintf('处理日期: %s\n', dateStr);
        fprintf('  加载雷达数据: %s\n', matFilePath);
        matData = load(matFilePath);
        
        % 检查必要的变量是否存在
        if ~isfield(matData, 'ze') || ~isfield(matData, 'obs_time')
            fprintf('警告: %s 中缺少必要的变量 (ze 或 obs_time)，跳过此文件\n', matFiles(i).name);
            continue;
        end
        
        % 提取变量到当前工作区
        ze = matData.ze;
        obs_time = matData.obs_time;
        
        % 检查并处理 range_ka 变量
        if isfield(matData, 'range_ka')
            range_ka = matData.range_ka;
        else
            fprintf('  警告: 文件中没有 range_ka 变量，创建默认高度向量\n');
            range_ka = linspace(0, 4, 340)';
        end
        
        % 计算DWR
        ze.dwr = ze.ka - ze.w;
        range_wka = range_ka(1:min(340, length(range_ka)));
        
        % 加载过冷水识别结果
        fprintf('  加载过冷水识别结果: %s\n', supercooledDataFile);
        supercooledData = load(supercooledDataFile);
        supercooled_info = supercooledData.supercooled_data;
        
        % 设置日期范围
        start_day = datenum(str2double(dateParts{1}), str2double(dateParts{2}), str2double(dateParts{3}));
        end_day = start_day;
        
        % 按天绘制
        day_edges = start_day:end_day;
        for d = day_edges
            mask = (obs_time >= d) & (obs_time < d+1);
            if ~any(mask)
                fprintf('  警告: 日期 %s 没有雷达数据，跳过\n', datestr(d,'yyyy-mm-dd'));
                continue;
            end
            
            ot = obs_time(mask);
            zk = ze.ka(:,mask);
            zd = ze.dwr(:,mask);
            
            % 创建整页大图
            fig = figure('position',[50 50 1800 1400], 'PaperUnits', 'inches', 'PaperPosition', [0 0 figsize]);
            
            % 计算统一的时间范围
            xrange = [d, d+1];  % 完整的一天范围
            
            % 设置子图位置参数 - 2x2布局
            titleSpace = 0.05;
            axHeight = 0.35;    % 增加子图高度
            axWidth = 0.38;     % 子图宽度
            axGapV = 0.08;      % 垂直间隔
            axGapH = 0.04;      % 水平间隔
            axLeft1 = 0.04;     % 左列左边距
            axLeft2 = 0.50;     % 右列左边距
            cbWidth = 0.015;    % 色标宽度
            cbGap = 0.008;      % 色标和图之间的间隔
            cbGap2 = 0.025;     % 两个色标之间的间隔
            
            % 设置顶部的起始位置
            topStart = 1 - titleSpace;

            % 定义2x2布局的子图位置
            axPos1 = [axLeft1, topStart-axHeight, axWidth, axHeight];           % 左上：Ka-band + 过冷水等值线
            axPos2 = [axLeft2, topStart-axHeight, axWidth, axHeight];           % 右上：DWR + 过冷水等值线
            axPos3 = [axLeft1, topStart-(2*axHeight+axGapV), axWidth, axHeight]; % 左下：Ka-band 原始
            axPos4 = [axLeft2, topStart-(2*axHeight+axGapV), axWidth, axHeight]; % 右下：DWR 原始

            % 色标位置参数
            cb1Pos = [axLeft1+axWidth+cbGap, topStart-axHeight, cbWidth, axHeight];
            cbSuper1Pos = [axLeft1+axWidth+cbGap+cbWidth+cbGap2, topStart-axHeight, cbWidth, axHeight];
            cb2Pos = [axLeft2+axWidth+cbGap, topStart-axHeight, cbWidth, axHeight];
            cbSuper2Pos = [axLeft2+axWidth+cbGap+cbWidth+cbGap2, topStart-axHeight, cbWidth, axHeight];
            cb3Pos = [axLeft1+axWidth+cbGap, topStart-(2*axHeight+axGapV), cbWidth, axHeight];
            cb4Pos = [axLeft2+axWidth+cbGap, topStart-(2*axHeight+axGapV), cbWidth, axHeight];
            
            % 在图上方添加标题
            titleText = sprintf('Radar Reflectivity vs Supercooled Water Detection – %s', datestr(d,'yyyy-mm-dd'));
            annotation('textbox', [0.1, 0.96, 0.8, 0.04], 'String', titleText, ...
                'FontWeight', 'bold', 'FontSize', 16, 'HorizontalAlignment', 'center', ...
                'VerticalAlignment', 'middle', 'LineStyle', 'none', 'FitBoxToText', 'off');
            
            % 确保数据维度匹配
            [ot_unique, ia, ~] = unique(ot, 'stable');
            zk_unique = zk(:, ia);
            zd_unique = zd(:, ia);
            
            [range_wka_unique, ib, ~] = unique(range_wka, 'stable');
            zk_unique = zk_unique(ib, :);
            zd_unique = zd_unique(ib, :);
            
            % 生成雷达回波与过冷水叠加图
            generate_radar_supercooled_overlay_plots(fig, ot_unique, range_wka_unique, zk_unique, zd_unique, ...
                supercooled_info, xrange, yrange, RdBu21, axPos1, axPos2, axPos3, axPos4, ...
                cb1Pos, cb2Pos, cb3Pos, cb4Pos, cbSuper1Pos, cbSuper2Pos);
            
            % 保存高质量图像
            hqfname = fullfile(outputDir, sprintf('%s_radar_supercooled_overlay.jpg', datestr(d,'yyyy-mm-dd')));
            
            try
                print(fig, hqfname, '-djpeg', '-r300');
                fprintf('  保存叠加图像: %s\n', hqfname);
            catch ME
                fprintf('  警告: 保存图像失败: %s\n', ME.message);
            end
            
            % 确保在关闭图形前所有绘图操作已完成
            drawnow;
            pause(0.5);
            
            % 关闭图形，释放内存
            try
                close(fig);
            catch
                fprintf('  警告: 关闭图形时出错\n');
            end
        end
        
    catch ME
        % 捕获并显示错误信息
        fprintf('处理文件 %s 时出错:\n', matFiles(i).name);
        fprintf('  错误信息: %s\n', ME.message);
        if ~isempty(ME.stack)
            fprintf('  错误位置: %s (第 %d 行)\n', ME.stack(1).name, ME.stack(1).line);
        end
    end
end

fprintf('批处理完成!\n');

%% -------- 子函数：生成雷达回波与过冷水叠加图 --------
function generate_radar_supercooled_overlay_plots(~, ot_unique, range_wka_unique, zk_unique, zd_unique, ...
    supercooled_info, xrange, yrange, RdBu21, axPos1, axPos2, axPos3, axPos4, ...
    cb1Pos, cb2Pos, cb3Pos, cb4Pos, cbSuper1Pos, cbSuper2Pos)

    % 准备过冷水数据用于等值线绘制
    % 将时间从小时转换为datenum格式以匹配雷达时间
    supercooled_time_datenum = xrange(1) + supercooled_info.time_hours / 24;
    supercooled_height_km = supercooled_info.height_km;
    supercooled_mask = double(supercooled_info.supercooled_mask);

    % 将过冷水掩码转换为连续数值，便于等值线绘制
    supercooled_contour_data = supercooled_mask;
    supercooled_contour_data(supercooled_mask == 0) = NaN;  % 无过冷水区域设为NaN

    % 1. Ka-band 雷达图 + 过冷水等值线
    ax1 = subplot(2,2,1);
    pcolor(ot_unique, range_wka_unique, zk_unique);
    clim([-30 20]);
    shading flat;
    ylim(yrange); axis xy; colormap(gca, RdBu21);
    set(ax1, 'Position', axPos1);

    % 叠加过冷水等值线
    hold(ax1, 'on');
    try
        % 创建过冷水等值线 - 使用0.5作为等值线级别
        contour_levels = 0.5;
        [~, ~] = contour(ax1, supercooled_time_datenum, supercooled_height_km, supercooled_contour_data, ...
            contour_levels, 'LineWidth', 2, 'LineColor', 'magenta');

        % 为过冷水等值线创建独立色标
        axSuper1 = axes('Position', axPos1, 'Color', 'none', 'Visible', 'off');
        colormap(axSuper1, [1 1 1; 1 0 1]);  % 白色背景，洋红色过冷水
        cbSuper1 = colorbar(axSuper1, 'Location', 'eastoutside', 'Position', cbSuper1Pos);
        ylabel(cbSuper1, 'Supercooled Water', 'FontSize', 12);
        clim(axSuper1, [0 1]);
        set(cbSuper1, 'FontSize', 12, 'LineWidth', 0.5);
        set(cbSuper1, 'TickLength', [0.01, 0.025]);
        set(cbSuper1, 'Ticks', [0, 1], 'TickLabels', {'No', 'Yes'});

        fprintf('  成功添加过冷水等值线到Ka图\n');
    catch ME
        fprintf('  在Ka图上添加过冷水等值线失败: %s\n', ME.message);
    end
    hold(ax1, 'off');

    % 确保后续操作作用于ax1
    axes(ax1);
    title('(a) Ka-band Reflectivity + Supercooled Water', 'FontSize', 12, 'FontWeight', 'bold');
    xlim(xrange);
    ylabel('Height (km)', 'FontSize', 12);
    set(ax1, 'FontSize', 12);
    set(ax1, 'XTick', xrange(1)+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
    set(ax1, 'YTick', 0:1:4);
    grid off; box off;
    set(ax1, 'TickDir', 'out', 'TickLength', [0.01, 0.025]);

    % 添加边框线
    line(ax1, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5);
    line(ax1, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5);

    % 添加Ka-band色标
    cb1 = colorbar('Position', cb1Pos);
    ylabel(cb1, 'Ka-band reflectivity [dBZ]', 'FontSize', 12);
    set(cb1, 'FontSize', 12, 'LineWidth', 0.5, 'TickLength', [0.01, 0.025]);

    % 2. DWR 雷达图 + 过冷水等值线
    ax2 = subplot(2,2,2);
    pcolor(ot_unique, range_wka_unique, zd_unique);
    clim([-2 15]);
    shading flat;
    ylim(yrange); axis xy; colormap(gca, RdBu21);
    set(ax2, 'Position', axPos2);

    % 叠加过冷水等值线
    hold(ax2, 'on');
    try
        % 创建过冷水等值线
        [~, ~] = contour(ax2, supercooled_time_datenum, supercooled_height_km, supercooled_contour_data, ...
            contour_levels, 'LineWidth', 2, 'LineColor', 'magenta');

        % 为过冷水等值线创建独立色标
        axSuper2 = axes('Position', axPos2, 'Color', 'none', 'Visible', 'off');
        colormap(axSuper2, [1 1 1; 1 0 1]);
        cbSuper2 = colorbar(axSuper2, 'Location', 'eastoutside', 'Position', cbSuper2Pos);
        ylabel(cbSuper2, 'Supercooled Water', 'FontSize', 12);
        clim(axSuper2, [0 1]);
        set(cbSuper2, 'FontSize', 12, 'LineWidth', 0.5);
        set(cbSuper2, 'TickLength', [0.01, 0.025]);
        set(cbSuper2, 'Ticks', [0, 1], 'TickLabels', {'No', 'Yes'});

        fprintf('  成功添加过冷水等值线到DWR图\n');
    catch ME
        fprintf('  在DWR图上添加过冷水等值线失败: %s\n', ME.message);
    end
    hold(ax2, 'off');

    % 确保后续操作作用于ax2
    axes(ax2);
    title('(b) DWR + Supercooled Water', 'FontSize', 12, 'FontWeight', 'bold');
    xlim(xrange);
    ylabel('Height (km)', 'FontSize', 12);
    set(ax2, 'FontSize', 12);
    set(ax2, 'XTick', xrange(1)+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
    set(ax2, 'YTick', 0:1:4);
    grid off; box off;
    set(ax2, 'TickDir', 'out', 'TickLength', [0.01, 0.025]);

    % 添加边框线
    line(ax2, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5);
    line(ax2, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5);

    % 添加DWR色标
    cb2 = colorbar('Position', cb2Pos);
    ylabel(cb2, 'DWR [dB]', 'FontSize', 12);
    set(cb2, 'FontSize', 12, 'LineWidth', 0.5, 'TickLength', [0.01, 0.025]);

    % 3. Ka-band 雷达图（原始，无叠加）
    ax3 = subplot(2,2,3);
    pcolor(ot_unique, range_wka_unique, zk_unique);
    clim([-30 20]);
    shading flat;
    ylim(yrange); axis xy; colormap(gca, RdBu21);
    set(ax3, 'Position', axPos3);

    title('(c) Ka-band Reflectivity (Original)', 'FontSize', 12, 'FontWeight', 'bold');
    xlim(xrange);
    ylabel('Height (km)', 'FontSize', 12);
    set(ax3, 'FontSize', 12);
    set(ax3, 'XTick', xrange(1)+(0:2:24)/24, 'XMinorTick', 'on');
    datetick(ax3, 'x', 'HH:MM', 'keeplimits', 'keepticks');
    xlabel('Time [BJT]', 'FontSize', 12);
    set(ax3, 'YTick', 0:1:4);
    grid off; box off;
    set(ax3, 'TickDir', 'out', 'TickLength', [0.01, 0.025]);

    % 添加边框线
    line(ax3, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5);
    line(ax3, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5);

    % 添加Ka-band色标
    cb3 = colorbar('Position', cb3Pos);
    ylabel(cb3, 'Ka-band reflectivity [dBZ]', 'FontSize', 12);
    set(cb3, 'FontSize', 12, 'LineWidth', 0.5, 'TickLength', [0.01, 0.025]);

    % 4. DWR 雷达图（原始，无叠加）
    ax4 = subplot(2,2,4);
    pcolor(ot_unique, range_wka_unique, zd_unique);
    clim([-2 15]);
    shading flat;
    ylim(yrange); axis xy; colormap(gca, RdBu21);
    set(ax4, 'Position', axPos4);

    title('(d) DWR (Original)', 'FontSize', 12, 'FontWeight', 'bold');
    xlim(xrange);
    ylabel('Height (km)', 'FontSize', 12);
    set(ax4, 'FontSize', 12);
    set(ax4, 'XTick', xrange(1)+(0:2:24)/24, 'XMinorTick', 'on');
    datetick(ax4, 'x', 'HH:MM', 'keeplimits', 'keepticks');
    xlabel('Time [BJT]', 'FontSize', 12);
    set(ax4, 'YTick', 0:1:4);
    grid off; box off;
    set(ax4, 'TickDir', 'out', 'TickLength', [0.01, 0.025]);

    % 添加边框线
    line(ax4, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5);
    line(ax4, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5);

    % 添加DWR色标
    cb4 = colorbar('Position', cb4Pos);
    ylabel(cb4, 'DWR [dB]', 'FontSize', 12);
    set(cb4, 'FontSize', 12, 'LineWidth', 0.5, 'TickLength', [0.01, 0.025]);

    % 添加过冷水识别参数信息
    info_text = sprintf('Supercooled Water Detection:\nDepol < %.2f, BackScatter > %.2f\nCoverage: %.1f%%', ...
        supercooled_info.optimal_depol, supercooled_info.optimal_backscatter, supercooled_info.coverage_rate);

    annotation('textbox', [0.02, 0.02, 0.25, 0.15], 'String', info_text, ...
        'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
        'FitBoxToText', 'on');
end
