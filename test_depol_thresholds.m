function test_depol_thresholds()
% 测试不同Depol阈值对过冷水识别的影响
% 重点测试 0.1, 0.2, 0.3 等更严格的阈值

clc; clear; close all;

%% -------- 设置路径 --------
lidarDir = 'D:\lidar\supercooledwaterday-hourly';
backscatterDir = 'D:\lidar\backscatter';
era5Dir = 'D:\lidar\supercooledwaterday-hourly\era5';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\depol_threshold_test';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 参数设置 --------
% 过冷水温度范围
temp_max = 0;    % 最高温度 0°C
temp_min = -40;  % 最低温度 -40°C

% 重点测试的Depol阈值
depol_test_values = [0.1, 0.2, 0.3];

% 固定的Backscatter阈值
backscatter_threshold = 0.05;

% CloudMask参数
cloudmask_params = struct();
cloudmask_params.F_threshold = 800;
cloudmask_params.F2_threshold = 16;
cloudmask_params.maxHeight = 15.0;
cloudmask_params.k = 6;

fprintf('Depol阈值测试参数:\n');
fprintf('  测试Depol阈值: [%.1f, %.1f, %.1f]\n', depol_test_values);
fprintf('  固定Backscatter阈值: %.2f\n', backscatter_threshold);
fprintf('  过冷水温度范围: %.1f°C 到 %.1f°C\n', temp_min, temp_max);

%% -------- 查找数据文件 --------
depolFiles = dir(fullfile(lidarDir, 'Depol_*.csv'));
fprintf('\n找到 %d 个Depol文件\n', length(depolFiles));

if isempty(depolFiles)
    error('未找到Depol文件，请检查数据路径');
end

% 选择几个代表性的文件进行测试
test_files = min(3, length(depolFiles));  % 测试前3个文件
fprintf('将测试前 %d 个文件\n', test_files);

%% -------- 处理文件 --------
all_results = [];

for fileIdx = 1:test_files
    try
        % 获取文件信息
        depolFile = depolFiles(fileIdx).name;
        fprintf('\n=== 处理文件 %d/%d: %s ===\n', fileIdx, test_files, depolFile);
        
        % 从文件名提取日期
        dateMatch = regexp(depolFile, 'Depol_(\d{8})', 'tokens');
        if isempty(dateMatch)
            fprintf('  警告: 无法从文件名解析日期，跳过\n');
            continue;
        end
        dateStr = dateMatch{1}{1};
        dateFormatted = [dateStr(1:4), '-', dateStr(5:6), '-', dateStr(7:8)];
        
        % 查找对应文件
        backscatterFile = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', dateStr));
        backscatterFiles = dir(backscatterFile);
        if isempty(backscatterFiles)
            fprintf('  警告: 找不到对应的BackScatter文件，跳过\n');
            continue;
        end
        
        era5File = fullfile(era5Dir, sprintf('era5_temp_geopotential_%s.nc', dateStr));
        if ~exist(era5File, 'file')
            fprintf('  警告: 找不到对应的ERA5文件，跳过\n');
            continue;
        end
        
        % 加载数据
        [lidar_data, success] = load_lidar_data_test(lidarDir, backscatterDir, depolFile, backscatterFiles(1).name);
        if ~success
            fprintf('  错误: 激光雷达数据加载失败\n');
            continue;
        end
        
        [era5_data, success] = load_era5_data_test(era5File);
        if ~success
            fprintf('  错误: ERA5数据加载失败\n');
            continue;
        end
        
        % CloudMask云识别
        [cloud_mask, cloud_stats] = identify_clouds_test(lidar_data, cloudmask_params);
        fprintf('  CloudMask云识别完成，云覆盖率: %.1f%%\n', sum(cloud_mask(:))/numel(cloud_mask)*100);
        
        % 温度约束
        [temp_mask, height_info] = apply_temperature_constraint_test(lidar_data, era5_data, temp_min, temp_max);
        fprintf('  温度约束完成，过冷区域覆盖率: %.1f%%\n', sum(temp_mask(:))/numel(temp_mask)*100);
        
        % 综合约束条件
        base_mask = cloud_mask & temp_mask;
        fprintf('  综合约束后覆盖率: %.1f%%\n', sum(base_mask(:))/numel(base_mask)*100);
        
        % 测试不同Depol阈值
        depol_results = test_different_depol_thresholds(lidar_data, base_mask, depol_test_values, backscatter_threshold);
        
        % 保存结果
        file_result = struct();
        file_result.date = dateFormatted;
        file_result.height_info = height_info;
        file_result.cloud_coverage = sum(cloud_mask(:))/numel(cloud_mask)*100;
        file_result.temp_coverage = sum(temp_mask(:))/numel(temp_mask)*100;
        file_result.base_coverage = sum(base_mask(:))/numel(base_mask)*100;
        file_result.depol_results = depol_results;
        
        all_results = [all_results; file_result];
        
        % 生成对比图
        generate_depol_comparison_plot(lidar_data, era5_data, base_mask, height_info, depol_results, dateFormatted, outputDir);
        
        fprintf('  处理完成: %s\n', dateFormatted);
        
    catch ME
        fprintf('  处理文件 %s 时出错: %s\n', depolFile, ME.message);
        continue;
    end
end

%% -------- 生成汇总分析 --------
if ~isempty(all_results)
    generate_summary_analysis(all_results, depol_test_values, outputDir);
    fprintf('\n所有文件处理完成！\n');
    fprintf('结果保存在: %s\n', outputDir);
else
    fprintf('\n没有成功处理的文件\n');
end

end

%% -------- 子函数：测试不同Depol阈值 --------
function depol_results = test_different_depol_thresholds(lidar_data, base_mask, depol_values, backscatter_threshold)
    fprintf('    测试不同Depol阈值的效果...\n');
    
    depol_results = struct();
    depol_results.thresholds = depol_values;
    depol_results.detection_counts = zeros(size(depol_values));
    depol_results.coverage_rates = zeros(size(depol_values));
    
    base_count = sum(base_mask(:));
    
    for i = 1:length(depol_values)
        depol_thresh = depol_values(i);
        
        % 应用阈值条件
        depol_condition = lidar_data.depol < depol_thresh;
        backscatter_condition = lidar_data.backscatter > backscatter_threshold;
        
        % 综合所有条件
        supercooled_mask = base_mask & depol_condition & backscatter_condition;
        
        % 计算识别像素数和覆盖率
        detection_count = sum(supercooled_mask(:));
        if base_count > 0
            coverage_rate = detection_count / base_count * 100;
        else
            coverage_rate = 0;
        end
        
        depol_results.detection_counts(i) = detection_count;
        depol_results.coverage_rates(i) = coverage_rate;
        
        fprintf('      Depol < %.1f: 检测 %d 像素, 覆盖率 %.1f%%\n', ...
                depol_thresh, detection_count, coverage_rate);
    end
end

%% -------- 子函数：加载激光雷达数据 --------
function [lidar_data, success] = load_lidar_data_test(lidarDir, backscatterDir, depolFile, backscatterFile)
    success = false;
    lidar_data = struct();

    try
        % 加载数据
        depolPath = fullfile(lidarDir, depolFile);
        raw_depol = readmatrix(depolPath);

        backscatterPath = fullfile(backscatterDir, backscatterFile);
        raw_backscatter = readmatrix(backscatterPath);

        % 处理高度数据
        height = raw_depol(:,1);
        valid_idx = ~isnan(height);
        height = height(valid_idx);

        % 限制高度范围到4km以内
        height_mask = height <= 4000;
        height = height(height_mask);

        % 获取24小时数据
        max_time_points = 1440;
        depol_data = raw_depol(valid_idx, 2:min(end, max_time_points+1));
        depol_data = depol_data(height_mask, :);

        backscatter_data = raw_backscatter(valid_idx, 2:min(end, max_time_points+1));
        backscatter_data = backscatter_data(height_mask, :);

        % 创建时间向量
        num_times = size(depol_data, 2);
        time_vector = 0:(num_times-1);

        % 存储数据
        lidar_data.height = height;
        lidar_data.time = time_vector;
        lidar_data.depol = double(depol_data);
        lidar_data.backscatter = double(backscatter_data);

        fprintf('    激光雷达数据加载成功 (高度: %.0f-%.0f m, 时间: %d 分钟)\n', ...
                min(height), max(height), num_times);

        success = true;

    catch ME
        fprintf('    激光雷达数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：加载ERA5数据 --------
function [era5_data, success] = load_era5_data_test(era5File)
    success = false;
    era5_data = struct();

    try
        % 读取ERA5数据
        temperature = ncread(era5File, 't');
        geopotential = ncread(era5File, 'z');
        pressure_levels = ncread(era5File, 'pressure_level');
        time = ncread(era5File, 'valid_time');

        % 转换单位
        temperature_celsius = squeeze(temperature) - 273.15;
        geopotential_height = squeeze(geopotential) / 9.80665;

        % 存储数据
        era5_data.temperature = temperature_celsius;
        era5_data.height = geopotential_height;
        era5_data.pressure = pressure_levels;
        era5_data.time = time;

        fprintf('    ERA5数据加载成功\n');
        success = true;

    catch ME
        fprintf('    ERA5数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：简化的CloudMask云识别 --------
function [cloud_mask, cloud_stats] = identify_clouds_test(lidar_data, params)
    % 简化版本，直接使用阈值方法
    fprintf('    应用简化CloudMask算法...\n');

    % 使用简单的后向散射阈值来识别云层
    cloud_threshold = 1e-5;  % 后向散射阈值
    cloud_mask = lidar_data.backscatter > cloud_threshold;

    % 统计信息
    cloud_stats = struct();
    cloud_stats.threshold_detections = sum(cloud_mask(:));
    cloud_stats.water_cloud_detections = 0;
    cloud_stats.gradient_detections = 0;
end

%% -------- 子函数：温度约束 --------
function [temp_mask, height_info] = apply_temperature_constraint_test(lidar_data, era5_data, temp_min, temp_max)
    fprintf('    应用过冷水温度约束...\n');

    % 初始化温度掩码
    temp_mask = false(size(lidar_data.depol));

    % 计算平均温度剖面
    mean_temp_profile = mean(era5_data.temperature, 2);
    mean_height_profile = mean(era5_data.height, 2);

    % 插值到激光雷达高度网格
    temp_interp_mean = interp1(mean_height_profile, mean_temp_profile, lidar_data.height, 'linear', 'extrap');

    % 找到过冷水温度范围对应的高度范围
    supercooled_indices = find((temp_interp_mean >= temp_min) & (temp_interp_mean <= temp_max));

    if ~isempty(supercooled_indices)
        supercooled_height_min = lidar_data.height(min(supercooled_indices));
        supercooled_height_max = lidar_data.height(max(supercooled_indices));

        % 找到0°C和-40°C对应的具体高度
        [~, zero_idx] = min(abs(temp_interp_mean - 0));
        [~, minus40_idx] = min(abs(temp_interp_mean - (-40)));

        zero_height = lidar_data.height(zero_idx);
        minus40_height = lidar_data.height(minus40_idx);

        height_info = struct();
        height_info.supercooled_range = [supercooled_height_min, supercooled_height_max];
        height_info.zero_height = zero_height;
        height_info.minus40_height = minus40_height;

        fprintf('    过冷水高度范围: %.0f - %.0f m\n', supercooled_height_min, supercooled_height_max);
    else
        height_info = struct();
        height_info.supercooled_range = [NaN, NaN];
        height_info.zero_height = NaN;
        height_info.minus40_height = NaN;

        fprintf('    警告: 未找到过冷水温度区域\n');
    end

    % 对每个时间点应用温度约束
    for t = 1:length(lidar_data.time)
        era5_time_idx = min(t, size(era5_data.temperature, 2));
        temp_profile = era5_data.temperature(:, era5_time_idx);
        height_profile = era5_data.height(:, era5_time_idx);
        temp_interp = interp1(height_profile, temp_profile, lidar_data.height, 'linear', 'extrap');
        temp_condition = (temp_interp >= temp_min) & (temp_interp <= temp_max);
        temp_mask(:, t) = temp_condition;
    end
end

%% -------- 子函数：生成Depol对比图 --------
function generate_depol_comparison_plot(lidar_data, era5_data, base_mask, height_info, depol_results, dateStr, outputDir)
    fprintf('    生成Depol阈值对比图...\n');

    % 创建图形
    fig = figure('Position', [100, 100, 1600, 1000]);

    % 时间轴（转换为小时）
    time_hours = lidar_data.time / 60;

    % 子图1: 原始Depol数据
    subplot(2, 3, 1);
    pcolor(time_hours, lidar_data.height/1000, lidar_data.depol);
    shading flat;
    colorbar;
    clim([0, 1]);
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('原始退偏振比 (Depol)');
    ylim([0, 4]);

    % 子图2: 基础掩码（云层+温度约束）
    subplot(2, 3, 2);
    pcolor(time_hours, lidar_data.height/1000, double(base_mask));
    shading flat;
    colormap(gca, [1 1 1; 0 0.8 1]);
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('基础掩码 (云层+过冷温度)');
    ylim([0, 4]);

    % 子图3-5: 不同Depol阈值的识别结果
    depol_values = depol_results.thresholds;
    backscatter_threshold = 0.05;

    for i = 1:length(depol_values)
        subplot(2, 3, i+2);

        % 应用当前Depol阈值
        depol_condition = lidar_data.depol < depol_values(i);
        backscatter_condition = lidar_data.backscatter > backscatter_threshold;
        supercooled_mask = base_mask & depol_condition & backscatter_condition;

        pcolor(time_hours, lidar_data.height/1000, double(supercooled_mask));
        shading flat;
        colormap(gca, [1 1 1; 0 0.8 0.8]);
        xlabel('时间 (小时)');
        ylabel('高度 (km)');
        title(sprintf('Depol < %.1f\n覆盖率: %.1f%%', ...
                      depol_values(i), depol_results.coverage_rates(i)));
        ylim([0, 4]);
    end

    % 子图6: 统计对比
    subplot(2, 3, 6);
    bar(depol_values, depol_results.coverage_rates, 'FaceColor', [0.3 0.6 0.9]);
    xlabel('Depol阈值');
    ylabel('覆盖率 (%)');
    title('不同Depol阈值的覆盖率对比');
    grid on;

    % 添加数值标签
    for i = 1:length(depol_values)
        text(depol_values(i), depol_results.coverage_rates(i) + max(depol_results.coverage_rates)*0.02, ...
             sprintf('%.1f%%', depol_results.coverage_rates(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 添加总标题
    if ~isnan(height_info.supercooled_range(1))
        title_str = sprintf('Depol阈值测试 - %s (过冷水高度: %.0f-%.0f m)', ...
                           dateStr, height_info.supercooled_range(1), height_info.supercooled_range(2));
    else
        title_str = sprintf('Depol阈值测试 - %s', dateStr);
    end
    sgtitle(title_str, 'FontSize', 14, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, sprintf('depol_threshold_test_%s.png', strrep(dateStr, '-', '')));
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('    保存图像: %s\n', outputFile);

    close(fig);
end

%% -------- 子函数：生成汇总分析 --------
function generate_summary_analysis(all_results, depol_values, outputDir)
    fprintf('\n生成汇总分析...\n');

    % 创建汇总图
    fig = figure('Position', [100, 100, 1400, 800]);

    % 提取数据
    dates = {all_results.date};
    num_files = length(all_results);

    % 子图1: 不同日期的覆盖率对比
    subplot(2, 2, 1);
    coverage_matrix = zeros(num_files, length(depol_values));
    for i = 1:num_files
        coverage_matrix(i, :) = all_results(i).depol_results.coverage_rates;
    end

    bar(coverage_matrix);
    xlabel('日期');
    ylabel('覆盖率 (%)');
    title('不同日期的Depol阈值效果对比');
    legend(arrayfun(@(x) sprintf('Depol<%.1f', x), depol_values, 'UniformOutput', false), 'Location', 'best');
    set(gca, 'XTickLabel', dates);
    xtickangle(45);
    grid on;

    % 子图2: 平均覆盖率
    subplot(2, 2, 2);
    mean_coverage = mean(coverage_matrix, 1);
    std_coverage = std(coverage_matrix, 1);

    errorbar(depol_values, mean_coverage, std_coverage, 'o-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel('Depol阈值');
    ylabel('平均覆盖率 (%)');
    title('不同Depol阈值的平均效果');
    grid on;

    % 添加数值标签
    for i = 1:length(depol_values)
        text(depol_values(i), mean_coverage(i) + std_coverage(i) + max(mean_coverage)*0.05, ...
             sprintf('%.1f±%.1f%%', mean_coverage(i), std_coverage(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 子图3: 过冷水高度范围统计
    subplot(2, 2, 3);
    height_ranges = [];
    valid_dates = {};
    for i = 1:num_files
        if ~isnan(all_results(i).height_info.supercooled_range(1))
            height_ranges = [height_ranges; all_results(i).height_info.supercooled_range];
            valid_dates{end+1} = all_results(i).date;
        end
    end

    if ~isempty(height_ranges)
        bar(1:size(height_ranges,1), height_ranges);
        xlabel('日期');
        ylabel('高度 (m)');
        title('过冷水高度范围');
        legend('最低高度', '最高高度', 'Location', 'best');
        set(gca, 'XTickLabel', valid_dates);
        xtickangle(45);
        grid on;
    else
        text(0.5, 0.5, '无有效的过冷水高度数据', 'HorizontalAlignment', 'center');
        title('过冷水高度范围');
    end

    % 子图4: 统计表格
    subplot(2, 2, 4);
    axis off;

    % 创建统计文本
    stats_text = {
        '汇总统计:';
        '';
        sprintf('测试文件数: %d', num_files);
        sprintf('测试Depol阈值: [%.1f, %.1f, %.1f]', depol_values);
        '';
        '平均覆盖率:';
    };

    for i = 1:length(depol_values)
        stats_text{end+1} = sprintf('  Depol < %.1f: %.1f%% (±%.1f%%)', ...
                                   depol_values(i), mean_coverage(i), std_coverage(i));
    end

    stats_text = [stats_text; {
        '';
        '最佳阈值建议:';
        sprintf('  推荐Depol阈值: %.1f', depol_values(mean_coverage == max(mean_coverage)));
        sprintf('  最大平均覆盖率: %.1f%%', max(mean_coverage));
    }];

    text(0.1, 0.9, stats_text, 'FontSize', 10, 'VerticalAlignment', 'top', ...
         'HorizontalAlignment', 'left', 'Units', 'normalized');

    % 添加总标题
    sgtitle('Depol阈值测试汇总分析', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, 'depol_threshold_summary.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存汇总图像: %s\n', outputFile);

    % 保存数据
    save(fullfile(outputDir, 'depol_threshold_results.mat'), 'all_results', 'depol_values', 'mean_coverage', 'std_coverage');
    fprintf('保存汇总数据: %s\n', fullfile(outputDir, 'depol_threshold_results.mat'));

    close(fig);

    % 输出最终建议
    [~, best_idx] = max(mean_coverage);
    fprintf('\n=== 最终建议 ===\n');
    fprintf('推荐Depol阈值: %.1f\n', depol_values(best_idx));
    fprintf('平均覆盖率: %.1f%% (±%.1f%%)\n', mean_coverage(best_idx), std_coverage(best_idx));
    fprintf('相比其他阈值的优势:\n');
    for i = 1:length(depol_values)
        if i ~= best_idx
            improvement = mean_coverage(best_idx) - mean_coverage(i);
            fprintf('  vs Depol<%.1f: +%.1f%%\n', depol_values(i), improvement);
        end
    end
end
