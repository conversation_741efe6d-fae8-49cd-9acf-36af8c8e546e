# -*- coding:utf-8 -*-
# This code can detect the cloud and cloud phase.
from netCDF4 import Dataset
import sys
sys.path.append('E:/MPL/MPLcode/')
import numpy as np
import sys, h5py, matplotlib
#import mpl_sjy# as mpl
import pylib.arrs.mpl as mpl
from pylib.uselib.smooth import savitzky_golay
import pylib.uselib.smooth as smooth
import matplotlib.pyplot as plt;from matplotlib.pyplot import imshow as ims
import warnings
import datetime as dt
from multiprocessing import Pool
#from pylib.uselib.time import Datetime2TimeofYear
from pylib.uselib.time import Date2TimeofYear, TimeofYear2Datetime
#from pylib.arrs.read_sounding import get_sounding, read_sounding
from pylib.uselib.Radiosonde import ReadRadiosonde
import pandas as pd
from dateutil.parser import parse
from pylab import mpl as pylabmpl
from matplotlib.ticker import MultipleLocator, AutoMinorLocator
import cmaps
import matplotlib.colors as mcolors
import math, re
import matplotlib.dates as mdates
from pylib.plot.PlotStyle import set_axis
plt.rcParams['savefig.dpi'] = 300 #保存图片分辨率
plt.rcParams['figure.dpi'] = 300
from numpy import squeeze as sqz
from scipy.interpolate import splev, splrep
from pylab import mpl as pylabmpl
# import numba   
# @numba.njit

def read_avors(day, minute2average, path='F:/Avors/AvorsResivedData'):
    #path = 'H:/Avors/AvorsResivedData'
    filename = 'A Square of Distance_'
    filenameb = 'B Square of Distance_'
    #filename = 'Channel A Square of Range_'
    #filenameb = 'channel B Square of Range_'
    data = pd.read_csv(path+'/'+filename+day+'.csv',encoding='gbk',index_col='时间/距离(M)')
    datab = pd.read_csv(path+'/'+filenameb+day+'.csv',encoding='gbk',index_col='时间/距离(M)')
    data.index=pd.to_datetime(data.index)        
    datab.index=pd.to_datetime(datab.index)
    
    if minute2average==1:
        data_5mean=data
        datab_5mean=datab
    else:
        data_5mean=data.resample(str(minute2average)+'T').mean()
        datab_5mean=datab.resample(str(minute2average)+'T').mean()
    noisea = data.resample(str(minute2average)+'T').std()
    noiseb = datab.resample(str(minute2average)+'T').std()

    height=np.arange(15,30015,15)/1000
    height=height[3:]*math.sin(math.radians(75))
    time=data_5mean.index

    noisea = noisea.values[:,:]
    noiseb = noiseb.values[:,:]
    cha=data_5mean.values[:,:]
    chb=datab_5mean.values[:,:]

    nrb=cha+chb
    depolarization=chb/cha

    signal = {}
    signal['cha'] = cha
    signal['chb'] = chb
    signal['noisea'] = noisea
    signal['noiseb'] = noiseb
    signal['nrb'] = nrb
    signal['depolarization'] = depolarization
    signal['time'] = time
    signal['height'] = height
    return signal

def interp_avors(day, minute2average):
    #差值到15m，垂直的高度上，可以与MPL比较

    signal = read_avors(day, minute2average)
    cha = signal['cha']
    chb = signal['chb']
    noisea = signal['noisea']
    noiseb = signal['noiseb']

    height = np.arange(1,2186+1)*15/1000.
    cha_interp = np.zeros((cha.shape[0], 2186))
    chb_interp = np.zeros((chb.shape[0], 2186))
    noisea_interp = np.zeros((noisea.shape[0], 2186))
    noiseb_interp = np.zeros((noiseb.shape[0], 2186))

    for i in range(cha.shape[0]):
        cha_interp[i] = np.interp(height, signal['height'], cha[i])
        chb_interp[i] = np.interp(height, signal['height'], chb[i])
        noisea_interp[i] = np.interp(height, signal['height'], noisea[i])
        noiseb_interp[i] = np.interp(height, signal['height'], noiseb[i])
    SNRa = cha_interp/noisea_interp
    SNRb = chb_interp/noiseb_interp
    noise = np.sqrt(noisea_interp ** 2 + noiseb_interp ** 2)
    SNR = (cha_interp+chb_interp)/noise

    signal_interp = {}
    signal_interp['cha'] = cha_interp
    signal_interp['chb'] = chb_interp
    signal_interp['noisea'] = noisea_interp
    signal_interp['noiseb'] = noiseb_interp
    signal_interp['nrb'] = cha_interp + chb_interp
    signal_interp['depolarization'] = chb_interp / cha_interp
    signal_interp['time'] = signal['time']
    signal_interp['height'] = height
    signal_interp['SNRa'] = SNRa
    signal_interp['SNRb'] = SNRb
    signal_interp['SNR'] = SNR

    return signal_interp

def average_avors(day, minute2average):
    path = 'H:/Avors/AvorsResivedData'
    filename = 'A Square of Distance_'
    filenameb = 'B Square of Distance_'
    data = pd.read_csv(path+'/'+filename+day+'.csv',encoding='gbk',index_col='时间/距离(M)')
    datab = pd.read_csv(path+'/'+filenameb+day+'.csv',encoding='gbk',index_col='时间/距离(M)')
    data.index=pd.to_datetime(data.index)        
    datab.index=pd.to_datetime(datab.index)

    binsize = minute2average
    time0 = np.arange(0.,240.,binsize/6.)
    time0 = time0/10.
    time = time0[:]
    time0 = np.append(time0,[24.])
    Ntime = len(time)
#    for itime in range(Ntime):
#        time[itime] = (time0[itime]+time0[itime+1])/2.
    year = int(day[:4])
    DayofYear = Date2TimeofYear(year,int(day[4:6]),int(day[6:8]))
    TimeofYear = time/24.+DayofYear
    # t = np.array([])
    # channel1 = np.array([])
    # channel2 = np.array([])
    t_=data.index
    t = ((t_-parse(day)).total_seconds()/3600).values
    height=np.arange(15,30015,15)/1000
    Nbin = len(height)
    #Nt = len(t)
    channela = np.ones((Ntime,Nbin)) * np.nan
    channelb = np.ones((Ntime,Nbin)) * np.nan
    noisea = np.ones((Ntime,Nbin)) * np.nan
    noiseb = np.ones((Ntime,Nbin)) * np.nan
    cha=data.values[:,:]
    chb=datab.values[:,:]

    for itime in range(Ntime):
        t1 = t>=time0[itime]
        t2 = t<time0[itime+1]
        t_condition = t1*t2
        n = np.sum(t_condition)
        #print(t_condition)
        if np.any(t_condition)==True:
            #with warnings.catch_warnings():
            #    warnings.simplefilter("ignore", category=RuntimeWarning)
            channela[itime] = np.nanmean(cha[t_condition],axis=0)
            channelb[itime] = np.nanmean(chb[t_condition],axis=0)
            noisea[itime] = np.nanstd(cha[t_condition],axis=0,ddof=1)
            noiseb[itime] = np.nanstd(chb[t_condition],axis=0,ddof=1)
            #print(1)
        else:
            channela[itime] = np.nan
            channelb[itime] = np.nan
        
    data = {}
    data['time'] = TimeofYear
    data['height'] = height
    data['cha'] = channela
    data['chb'] = channelb
    data['noisea'] = noisea
    data['noiseb'] = noiseb
    data['nrb'] = channela + channelb
    return data

def PulseNumDetermine(day, time):
    '''
    用于得到确定每条廓线的时间分辨率，到底是10s，还是60s。
    分别对应了不同的PulseNum
    在Read_Raw函数中使用
    #PulseNum = 150000  #(60s)  大部分都是这个 default
    #PulseNum = 25000  #(10s)
    使用10s的时间段：
    2022.05.24 --> 2022.06.15
    2022.07.12 --> 2022.07.20 扫描, 需要进一步处理
    2022.08.12 --> 2022.08.21
    2022.08.30 --> 2022.10.16 

    #转化天 2022.05.24 2022.06.15 2022.08.12 2022.08.21 2022.08.30 2022.10.16    
    '''
    pn60 = 150000
    pn10 = 25000
    
    changedays = ['20220524','20220615','20221016','20220821','20220812','20220830']
    date = dt.datetime.strptime(day, '%Y%m%d')
    # period 以下条件为铁定60s的
    prd1 = date < dt.datetime(2022,5,24)
    prd3 = (date > dt.datetime(2022,6,15)) & (date < dt.datetime(2022,7,12))
    prd4 = (date > dt.datetime(2022,7,20)) & (date < dt.datetime(2022,8,12))
    prd5 = (date > dt.datetime(2022,8,21)) & (date < dt.datetime(2022,8,30))
    prd2 = date > dt.datetime(2022,10,16)
    
    # fineperiod
    fprd1 = parse('2022/5/24  16:10:07')
    fprd2 = parse('2022/6/15  11:54:24')
    fprd5 = parse('2022/8/12  14:14:13')
    fprd4 = parse('2022/8/21  15:23:54')
    fprd6 = parse('2022/8/30  18:10:04')
    fprd3 = parse('2022/10/16  21:45:26')

    if (prd1+prd2+prd3+prd4+prd5) > 0:
        PulseNum = pn60
    elif (prd1+prd2+prd3+prd4+prd5) == 0:
        PulseNum = pn10
    
    if day in changedays:   
        #PulseNum = np.zeros(len(time))
            # for ii in np.arange(len(PulseNum)):
            #     if time[ii] > fprd1:
            #         PulseNum[ii] = pn10
            #     else:
            #         PulseNum[ii] = pn60
        #PulseNum = 25000
        if day == '20220524':
            PulseNum = np.where(time > fprd1, pn10, pn60)
        elif day == '20220615':
            PulseNum = np.where(time > fprd2, pn60, pn10)
        elif day == '20221016':
            PulseNum = np.where(time > fprd3, pn60, pn10)
        elif day == '20220821':
            PulseNum = np.where(time > fprd4, pn60, pn10)
        elif day == '20220812':
            PulseNum = np.where(time > fprd5, pn10, pn60)
        elif day == '20220830':
            PulseNum = np.where(time > fprd6, pn10, pn60)

    #对7月11日-7月20日的要单独处理,扫描过的
    if (date >= dt.datetime(2022,7,11)) and (date <= dt.datetime(2022,7,20)):
        photon_df = pd.read_pickle(r'F:\Avors\code\temp_file\photon_df_20220711_20220720')
        phodf = photon_df.loc[day]
        PulseNum = phodf['Photon'].values
    return PulseNum

def Read_Raw(day, **kwargs):
    '''
    原始Avors数据为光子数，需要经过 1.deadtime 2.BackgroundAverage 
    然后做距离订正，除以Overlap，再除以固定能量（20us），得到单位为km^2/uJ/us的数据

    总脉冲数：
    PulseNum = 25000  (10s)
    PulseNum = 150000 (60s)

    对于光子数到计数率的转换 M = 光子数 / (PulseNum * 0.1 us)
    '''
    path = 'F:/Avors/AvorsRawData'
    #path = 'H:/Avors/AvorsRawData'
    filename = 'A Raw_'
    filenameb = 'B Raw_'
    data = pd.read_csv(path+'/'+filename+day+'.csv',encoding='gbk',index_col='时间/距离(M)')
    datab = pd.read_csv(path+'/'+filenameb+day+'.csv',encoding='gbk',index_col='时间/距离(M)')
    data.index=pd.to_datetime(data.index)
    datab.index=pd.to_datetime(datab.index)
    time = data.index
    A00 = data.values[:,:]
    B00 = datab.values[:,:]

    hgt=np.arange(15,30015,15)/1000

    # 需要加一些判断依据得到动态的PulseNum

    #deadtime correction
    #PulseNum = 150000  #(60s)
    #PulseNum = 25000  #(10s)
    PulseNum = PulseNumDetermine(day, time)

    A00 = A00.astype(float)
    B00 = B00.astype(float)
    # A00[A00 > 150000] = np.nan
    # B00[B00 > 150000] = np.nan
    #A00[A00 > 625000] = np.nan
    #B00[B00 > 625000] = np.nan

    A0 = A00
    B0 = B00

    if "deadtime" in kwargs.keys():
        t = kwargs['deadtime']  #ns
        # 这一段是根据滨松的非线性公式进行订正的
        #t = 20 #ns
        # t = 5 #ns
        if np.size(PulseNum) == 1:
            Ma = A00 / PulseNum / ((15 / 1.5e8) * 1e9) #1/ns
            Mb = B00 / PulseNum / ((15 / 1.5e8) * 1e9) #1/ns    
        else:
            Ma = A00 / PulseNum[:, np.newaxis] / ((15 / 1.5e8) * 1e9)
            Mb = B00 / PulseNum[:, np.newaxis] / ((15 / 1.5e8) * 1e9)
        CorFactorA = 1 / (1 - Ma * t)
        CorFactorB = 1 / (1 - Mb * t)

        A0 = A00 * CorFactorA
        B0 = B00 * CorFactorB
        print(f'dead time t={t} ns')
    else:
        print('No deadtime correction')
        pass

    # 这一段是根据滨松的非线性公式进行订正的，先取消了
    #t = 20 #ns
    # t = 5 #ns
    # if np.size(PulseNum) == 1:
    #     Ma = A00 / PulseNum / ((15 / 1.5e8) * 1e9) #1/ns
    #     Mb = B00 / PulseNum / ((15 / 1.5e8) * 1e9) #1/ns    
    # else:
    #     Ma = A00 / PulseNum[:, np.newaxis] / ((15 / 1.5e8) * 1e9)
    #     Mb = B00 / PulseNum[:, np.newaxis] / ((15 / 1.5e8) * 1e9)
    # CorFactorA = 1 / (1 - Ma * t)
    # CorFactorB = 1 / (1 - Mb * t)

    # A0 = A00 * CorFactorA 
    # B0 = B00 * CorFactorB


    # dz = 15 #m
    # #A0 = A00 / (1 - t * 0.15 * A00/ (M * dz))
    # A0 = A00 / 1#(1 - 0.1)

    # 发现加入非线性订正可能会使得订正过度， 即Ma过大，导致Ma * t <0
    # 目前暂时取消非线性订正

    #背景噪音校正
    Ntime = len(time)
    Ndatabin = len(hgt)
    bgA = np.zeros([Ntime,Ndatabin])
    bgB = np.zeros([Ntime,Ndatabin])
    for itime in range(Ntime):
        bgA[itime] = np.nanmean(A0[itime,-333:])   # cal mean signal of last 5000m as BackgroundAverage
        bgB[itime] = np.nanmean(B0[itime,-333:])
    uncertainA = np.sqrt(A0 + bgA)
    uncertainB = np.sqrt(B0 + bgB)

    #距离订正, overlap, 能量归一化，转为计数率   km^2/uJ/us
    # overlapdf = overlap_avors()
    # overlap0a = overlapdf['overlapA'].values
    # #overlap0b = overlapdf['overlapB'].values
    # overlap0b = overlapdf['overlapA'].values
    #这里尝试都使用A通道的Overlap值，以减小退偏比在底层的低值区

    overlap0a, ovlp = overlap_avors_dynamic(day)
    overlap0b = overlap0a
    #overlap0b, ovlp = overlap_avors_dynamic(day)

    #Rawa = (A00 - BackgroundAverageA)  * hgt **2 / overlap0a[:,1] / 20 / (PulseNum * 0.1)
    if np.size(PulseNum) == 1:
        correctionA = hgt **2 / 20 / (PulseNum * 0.1) / overlap0a #20 laser energy  150000 PulseNum
        correctionB = hgt **2 / 20 / (PulseNum * 0.1) / overlap0b
        Rawa = (A0 - bgA)  * correctionA
        Rawb = (B0 - bgB)  * correctionB
        uncerA = uncertainA * correctionA
        uncerB = uncertainB * correctionB
    else:
        correctionA = hgt **2 / 20 / overlap0a    # / (PulseNum * 0.1)
        correctionB = hgt **2 / 20 / overlap0b   # / (PulseNum * 0.1)
        Rawa = (A0 - bgA)  * correctionA / (PulseNum[:, np.newaxis] * 0.1)
        Rawb = (B0 - bgB)  * correctionB / (PulseNum[:, np.newaxis] * 0.1)
        uncerA = uncertainA * correctionA / (PulseNum[:, np.newaxis] * 0.1)
        uncerB = uncertainB * correctionB / (PulseNum[:, np.newaxis] * 0.1)
    #Rawa = (A0 - BackgroundAverageA)  * hgt **2 / 20 / (PulseNum * 0.1)
    #Rawb = B0 * hgt **2 / overlap0b[:,1] / 20
    
    Rawdata = {}
    Rawdata['time'] = time
    Rawdata['height'] = hgt
    Rawdata['Rawa'] = Rawa  #NRB_A
    Rawdata['Rawb'] = Rawb  #NRB_B
    Rawdata['bgA'] = bgA    # photo counts A   
    Rawdata['bgB'] = bgB
    Rawdata['uncerA'] = uncerA #和NRB量纲一样
    Rawdata['uncerB'] = uncerB

    return Rawdata

def average_Raw(day, minute2average, **kwargs):
    #把原始光子数得到的结果做时间平均
    binsize = minute2average
    time0 = np.arange(0.,240.,binsize/6.)
    time0 = time0/10.
    time = time0[:]
    time0 = np.append(time0,[24.])
    Ntime = len(time)
#    for itime in range(Ntime):
#        time[itime] = (time0[itime]+time0[itime+1])/2.
    year = int(day[:4])
    DayofYear = Date2TimeofYear(year,int(day[4:6]),int(day[6:8]))
    TimeofYear = time/24.+DayofYear
    # t = np.array([])
    # channel1 = np.array([])
    # channel2 = np.array([])
    if "deadtime" in kwargs.keys():
        t = kwargs['deadtime']  #ns
        Rawdata = Read_Raw(day, deadtime=t)
    else:
        Rawdata = Read_Raw(day)
    Rawa = Rawdata['Rawa']
    Rawb = Rawdata['Rawb']
    uncerA = Rawdata['uncerA']
    uncerB = Rawdata['uncerB']
    t_= Rawdata['time']#.index
    t = ((t_-parse(day)).total_seconds()/3600).values
    height=np.arange(15,30015,15)/1000
    Nbin = len(height)
    #Nt = len(t)
    chA = np.ones((Ntime,Nbin)) * np.nan
    chB = np.ones((Ntime,Nbin)) * np.nan
    uncertaintyA = np.ones((Ntime,Nbin)) * np.nan
    uncertaintyB = np.ones((Ntime,Nbin)) * np.nan
    for itime in range(Ntime):
        t1 = t>=time0[itime]
        t2 = t<time0[itime+1]
        t_condition = t1*t2
        n = np.sum(t_condition)
        #print(t_condition)
        if np.any(t_condition)==True:
            #with warnings.catch_warnings():
            #    warnings.simplefilter("ignore", category=RuntimeWarning)
            chA[itime] = np.nanmean(Rawa[t_condition],axis=0)
            chB[itime] = np.nanmean(Rawb[t_condition],axis=0)
            uncertaintyA[itime] = np.sqrt(np.nansum(uncerA[t_condition] ** 2,axis=0)) / n
            uncertaintyB[itime] = np.sqrt(np.nansum(uncerB[t_condition] ** 2,axis=0)) / n
        else:
            chA[itime] = np.nan
            chB[itime] = np.nan
    
    NRBstd = np.sqrt(uncertaintyA ** 2 + uncertaintyB ** 2)
    SNR = (chA+chB)/NRBstd
    SNRA = chA/uncertaintyA
    SNRB = chB/uncertaintyB

    #--------------LDR uncertainty---------------------
    partialLDRA = - chB / chA ** 2
    partialLDRB = 1 / chA
    LDRstd = np.sqrt((partialLDRA * uncertaintyA) ** 2 + (partialLDRB * uncertaintyB) ** 2)
    LDR = chB / chA
    nrb = chA + chB
    data = {}
    data['time'] = TimeofYear
    data['height'] = height
    data['chA'] = chA
    data['chB'] = chB
    data['uncertaintyA'] = uncertaintyA
    data['uncertaintyB'] = uncertaintyB
    data['NRBstd'] = NRBstd
    data['SNR'] = SNR
    data['SNRA'] = SNRA
    data['SNRB'] = SNRB
    data['LDRstd'] = LDRstd
    
    data['LDR'] = LDR
    data['nrb'] = nrb
    return data

def interp_Raw(day, minute2average, **kwargs):
    #差值均值数据到15m，垂直的高度上，可以与MPL比较

    if "deadtime" in kwargs.keys():
        t = kwargs['deadtime']  #ns
        signal = average_Raw(day, minute2average, deadtime=t)
    else:
        signal = average_Raw(day, minute2average)
    #signal = average_Raw(day, minute2average)
    cha = signal['chA']
    chb = signal['chB']
    noisea = signal['uncertaintyA']
    noiseb = signal['uncertaintyB']

    height = np.arange(1,2186+1)*15/1000.
    hgt = np.arange(15,30015,15)/1000
    alt = hgt * math.sin(math.radians(75))
    cha_interp = np.zeros((cha.shape[0], 2186))
    chb_interp = np.zeros((chb.shape[0], 2186))
    noisea_interp = np.zeros((noisea.shape[0], 2186))
    noiseb_interp = np.zeros((noiseb.shape[0], 2186))

    for i in range(cha.shape[0]):
        cha_interp[i] = np.interp(height, alt, cha[i])
        chb_interp[i] = np.interp(height, alt, chb[i])
        noisea_interp[i] = np.interp(height, alt, noisea[i])
        noiseb_interp[i] = np.interp(height, alt, noiseb[i])
    
    SNRa = cha_interp/noisea_interp
    SNRb = chb_interp/noiseb_interp
    noise = np.sqrt(noisea_interp ** 2 + noiseb_interp ** 2)
    SNR = (cha_interp+chb_interp)/noise

    signal_interp = {}
    signal_interp['cha'] = cha_interp
    signal_interp['chb'] = chb_interp
    signal_interp['uncertaintyA'] = noisea_interp
    signal_interp['uncertaintyB'] = noiseb_interp
    signal_interp['uncertainty'] = np.sqrt(noisea_interp ** 2 + noiseb_interp ** 2)
    signal_interp['nrb'] = cha_interp + chb_interp
    signal_interp['depolarization'] = chb_interp / cha_interp
    signal_interp['time'] = signal['time']
    signal_interp['height'] = height
    signal_interp['SNRa'] = SNRa
    signal_interp['SNRb'] = SNRb
    signal_interp['SNR'] = SNR

    partialLDRA = - chb_interp / cha_interp ** 2
    partialLDRB = 1 / cha_interp
    LDRstd = np.sqrt((partialLDRA * noisea_interp) ** 2 + (partialLDRB * noiseb_interp) ** 2)

    signal_interp['LDRstd'] = LDRstd

    return signal_interp

def calculate_dep_std_calibrated(day, minute2average):
    Delta_g = 0.0009450769139845069
    Delta_k_star = 0.0062476847470839935
    k_star = 0.9621973629179129
    g = 0.0327024848634895

    signal_interp = interp_Raw(day, minute2average)
    uncertaintyA = signal_interp['uncertaintyA']
    uncertaintyB = signal_interp['uncertaintyB']
    cha = signal_interp['cha']
    chb = signal_interp['chb']

    dep_std_square = ((uncertaintyB / chb) ** 2 + (uncertaintyA / cha) ** 2 + (Delta_k_star / k_star) ** 2) * (chb / (cha * k_star)) ** 2 + Delta_g ** 2
    dep_std = np.sqrt(dep_std_square)
    return dep_std
    #pass

def calculate_dep_std_calibrated_zenith(day, minute2average):
    Delta_g = 0.0009450769139845069
    Delta_k_star = 0.0062476847470839935
    k_star = 0.9621973629179129
    g = 0.0327024848634895

    signal = average_Raw(day, minute2average)
    uncertaintyA = signal['uncertaintyA']
    uncertaintyB = signal['uncertaintyB']
    cha = signal['chA']
    chb = signal['chB']

    dep_std_square = ((uncertaintyB / chb) ** 2 + (uncertaintyA / cha) ** 2 + (Delta_k_star / k_star) ** 2) * (chb / (cha * k_star)) ** 2 + Delta_g ** 2
    dep_std = np.sqrt(dep_std_square)
    return dep_std


def cloud_mask(signal):
#    F_threshold=2000
#    F2_threshold=8
    F_threshold=500
    F2_threshold=4    
    blind = 4
    height = signal['height'][blind:1800]
    nrb = signal['nrb'][:,blind:1800]
    #nrb = signal['cha'][:,blind:1800]
    #nrb = signal['channel2'][:,blind:]
    time = signal['time']
    Nbin = len(height)
    Ntime = len(time)
    maxHeight = 15.   #km
    z = height[height<=maxHeight]
    Nz = len(z)
    cloud = np.zeros([Ntime,Nz])
    # Pnoise is signal above 15km

    for itime in range(Ntime):
        # P is mpl backscatter signal without range correction
        if not np.isnan(nrb[itime,height == maxHeight]):
            P = nrb[itime]/height**2
            PM = nrb[itime]
            # calculate noise
            Pnoise = P[height>=maxHeight]
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", category=RuntimeWarning)
                sd = np.nanstd(Pnoise,ddof = 1)
            if np.isnan(sd):
                cloud[itime,:] = np.nan
                continue
            k = 6
            noise = k*sd
            
            # removing noise
            P = P[height<=maxHeight]     
            #Ps = savitzky_golay(P,3,0)
            Ps = smooth.moving_average(P,3)
            PD1 = Ps.copy()
            PD2 = Ps.copy()
            for zi in range(Nz-1):
                if ~(abs(PD1[zi+1]-PD1[zi])>=noise):
                    PD1[zi+1] = PD1[zi]
                if ~(abs(PD2[Nz-zi-2]-PD2[Nz-zi-1])>=noise):
                    PD2[Nz-zi-2] = PD2[Nz-zi-1]            
            PD = (PD1+PD2)/2.

            # ascending order
            Is = np.argsort(PD)
            Rs = PD[Is]
            MA = np.nanmax(Rs)
            MI = np.nanmin(Rs)
            PE = np.linspace(1,Nz,Nz)/Nz

            for i in range(Nz-1):
                if Rs[i+1]==Rs[i]:
                    PE[i+1]=PE[i]
            y = PE*(MA-MI)+MI

            PN = np.zeros(Nz)
            PN[Is] = y

            # baseline
            B = np.linspace(Nz,1,Nz)/Nz*(MA-MI)+MI
            base = []
            top = []
            Area = []
            F = []
            F2 = []
            DP = []
            J = []
            DPF = []
            F1 = 100*(PN-B)/MA
        
            for zi in range(Nz-1) :
                if PN[zi+1]>B[zi+1] and PN[zi]<=B[zi]:
                    s = 0.
                    if PN[zi+1]>PN[zi]:
                        #print z[zi+1]
                        s = PN[zi+1]-B[zi+1]
                    for i in range(Nz-zi-3):
                        if PN[zi+i+2]>B[zi+i+2]:
                            s = s+PN[zi+i+2]-B[zi+i+2]
                   
                        if PN[zi+i+2]<=B[zi+i+2]:
                            if i+1>=3.:
                                base.append(zi+1)
                                top.append(zi+i+2)
                                if s<=(B[zi+1]-B[zi+i+2])*(i+1)/2.:
                                    s = 0.
                                Area.append(s)
                            break
        
            Nlayer = len(base)

            ln_nrb = np.log(PN*z**2.)
            G = np.zeros(Nz)
            for zi in range(Nz-2):
                G[zi+1] = (ln_nrb[zi+2]-ln_nrb[zi]) / (z[zi+2]-z[zi])

            for ilayer in range(Nlayer):
                F = []
                F2 = []
                F1 = 100*(PN-B)/MA
                PMX = 0.
                for i in range(base[ilayer]):
                    PMX = PMX+PM[i]
                F = Area[ilayer]*PMX/(z[top[ilayer]]-z[base[ilayer]])/MA
                F2 = np.nanmax(F1[base[ilayer]:top[ilayer]+1])
                #if F>1000. or F2>5:
                #F_threshold=2000; 
                #F_threshold=5;
                #F2_threshold=8
                #F2_threshold=8
                if F>F_threshold or F2>F2_threshold: 
                    cloud[itime,base[ilayer]:top[ilayer]+1] = 1.

                #增加2km以下水云的判断力度   吴兆龙 2024.02.18
                if (z[base[ilayer]]<2) and (np.nanmedian(PM[base[ilayer]:top[ilayer]+1])>0.1) and (np.nanpercentile(PM[base[ilayer]:top[ilayer]+1],90)>1):
                    if F>1:
                        cloud[itime,base[ilayer]:top[ilayer]+1] = 1.
                    if np.nanmax(G[base[ilayer]+1:top[ilayer]+1])>3 or np.nanmin(G[base[ilayer]+1:top[ilayer]+1])<-7.:
                        cloud[itime,base[ilayer]:top[ilayer]+1] = 1.
        else:
            cloud[itime,:] = np.nan
    return z,cloud, F_threshold, F2_threshold, maxHeight

def cloud_phase_avors(cloud, signal, T):
    #T is temperature
        
    return

def Pcolormesh(day,time,data,vmin,vmax,hmin,hmax,t1,t2,majorinterval,minorinterval,titletext,cbarunit,**kwargs):
    '''
    一个比较一般的画各种Pcolormesh图的函数
    '''
    if "figsize" in kwargs.keys():
        figsize = kwargs['figsize']
    else:
        figsize = (8,4.5)
    from pylab import mpl as pylabmpl
    fig = plt.figure(figsize=figsize)
    ax = fig.add_axes((0.07,0.09,0.80,0.80))
    thiscmap = cmaps.BlAqGrYeOrReVi200
    height = np.arange(1,2186+1)*15/1000.
    #height = np.linspace(0.03,21,700)
    norm = pylabmpl.colors.Normalize(vmin=vmin,vmax=vmax)

    ax.pcolormesh(time,height[:], data.T[:,:],cmap=thiscmap,norm=norm)
    ax.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
    ax.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    TimeStart = dt.datetime(int(day[0:4]),int(day[4:6]),int(day[6:8]),t1,0,0)
    TimeEnd=TimeStart+dt.timedelta(hours=(t2-t1))
    ax.set_xlim(TimeStart,TimeEnd)
    ax.set_ylim(hmin,hmax)
    ax.set_xlabel('Local Time',fontsize=10, weight='semibold')
    ax.set_ylabel('Height (km)', fontsize=10, weight='semibold')
    bar = fig.add_axes((0.9,0.09,0.02,0.6))
    cbar = pylabmpl.colorbar.ColorbarBase(bar,cmap=thiscmap,norm=norm,orientation='vertical')
    #for ax in axs:
    ax.tick_params(axis='x', labelsize=10)
    ax.set_title(f'Avors Lidar {titletext} on {day}',fontsize=12, weight='semibold')
    cbar.ax.set_title(f'{cbarunit}',pad=20, weight='semibold')
    return fig


def ReadERA5(day):
    dir_name='D:/era5/yangyk/data/'
    #date='20220101'
    fname = day + '.nc'
    #timeindex='00'
    #timeindex='12'
    nc_data_path = dir_name+fname
    nc_obj = Dataset(nc_data_path)
    data = {}
    items = []
    for i, key in enumerate(nc_obj.variables.keys()):
        items.append(key)
        value = sqz(nc_obj.variables[key][:])
        if value.ndim == 2:
            data[items[i]] = np.flip(value,axis=1)
        elif key == 'level': #只用翻转和高度有关的
            data[items[i]] = np.flip(value)
        else:
            data[items[i]] = value
        #data[items[i]] = np.flip(value,axis=value.ndim-1)
        #data[items[i]] = sqz(nc_obj.variables[key][:])
    
    latitude = 40
    theta = latitude / 180 * math.pi
    g = 9.78049 * (1 + 0.005288 * (math.sin(theta)) **2 - 0.000006 * (math.sin(2 * theta)) ** 2)
    H = data['z'] / (g * 1000)#H位势高度
    data['H'] = H
    #print(nc_obj.variables.keys())
    return data

def ReadERA5_CST(day):
    #把世界时转换为北京时，取前一天的后8个小时和今天的前16个小时
    yesterday = parse(day) - dt.timedelta(days=1)
    day1 = yesterday.strftime('%Y%m%d')
    data = ReadERA5(day)
    datayes = ReadERA5(day1)
    dataCST = {}
    for key in data.keys():
        if (data[key].size != 1) and (data[key].shape[0] == 24):
            #dataCST[key] = np.concatenate((data[key][8:], datanext[key][:8]))
            dataCST[key] = np.concatenate((datayes[key][16:], data[key][:16]))
        else:
            dataCST[key] = data[key]
    return dataCST

def ReadERA5_CST_for_interp(day):
    #把世界时转换为北京时，取前一天的后8个小时和今天的前17个小时,共25小时用于插值
    yesterday = parse(day) - dt.timedelta(days=1)
    day1 = yesterday.strftime('%Y%m%d')
    data = ReadERA5(day)
    datayes = ReadERA5(day1)
    dataCST = {}
    for key in data.keys():
        if (data[key].size != 1) and (data[key].shape[0] == 24):
            #dataCST[key] = np.concatenate((data[key][8:], datanext[key][:8]))
            dataCST[key] = np.concatenate((datayes[key][16:], data[key][:17]))
        else:
            dataCST[key] = data[key]
    return dataCST

def CalRH(dataCST):
    #Ref He Yun AAS 2022
    #计算出相对于水和冰的相对湿度
    q = dataCST['q']
    p = dataCST['level']
    T = dataCST['t']
    r = q / (1-q) #注意单位。把比湿换成水汽混合比。
    #E = (q * p) / (0.622 + q)
    E = (r * p) / (0.622 + r)
    Ew = 6.112 * np.exp(17.67 * (T - 273.16) / (T - 29.65))
    Ei = 6.1078 * np.exp(21.87 * (T - 273.16)/ (T - 7.66))
    RHw = E / Ew
    RHi = E / Ei
    return RHw, RHi

def ReadERA5_interp(day, minute2average, heightindex=1100):
    #把ERA5数据差值到15m高度，时间分辨率和Avors一致
    dataCST = ReadERA5_CST_for_interp(day)
    time = dataCST['time']
    Time = [dt.datetime(1900, 1, 1) + dt.timedelta(hours=int(ii) + 8) for ii in time]
    freq = str(minute2average) + 'T'
    height = (np.arange(1,2186+1)*15/1000)[:heightindex]
    height_arr = np.tile(height, (int(1440 / minute2average),1))
    ti = []
    qi = []
    ui = []
    vi = []
    wi = []
    leveli = []
    level = dataCST['level']
    H = dataCST['H']
    q = dataCST['q']
    t = dataCST['t']
    u = dataCST['u']
    v = dataCST['v']
    w = dataCST['w']

    for ii in np.arange(np.shape(H)[0]):
        #splt = splrep(H[ii], t[ii])
        #ti_ = splev(height, splt)
        ti_ = np.interp(height,H[ii],t[ii])
        ti.append(ti_)
        qi_ = np.interp(height,H[ii],q[ii])    
        #spl = splrep(H[ii], q[ii])
        #qi_ = splev(height, spl)
        qi.append(qi_)
        ui_ = np.interp(height,H[ii],u[ii])
        ui.append(ui_)
        vi_ = np.interp(height,H[ii],v[ii])
        vi.append(vi_)
        wi_ = np.interp(height,H[ii],w[ii])
        wi.append(wi_)              
        #level_ = np.interp(height,H[ii],level)
        spl = splrep(H[ii], level)
        level_ = splev(height, spl)
        leveli.append(level_)
    ti = np.array(ti)
    qi = np.array(qi)
    ui = np.array(ui)
    vi = np.array(vi)
    wi = np.array(wi)
    leveli = np.array(leveli)

    ti_df = pd.DataFrame(index = Time, data = ti)
    ti_df_interp = ti_df.resample(freq).interpolate(method='linear').iloc[:-1,:]

    qi_df = pd.DataFrame(index = Time, data = qi)
    qi_df_interp = qi_df.resample(freq).interpolate(method='linear').iloc[:-1,:]

    ui_df = pd.DataFrame(index = Time, data = ui)
    ui_df_interp = ui_df.resample(freq).interpolate(method='linear').iloc[:-1,:]

    vi_df = pd.DataFrame(index = Time, data = vi)
    vi_df_interp = vi_df.resample(freq).interpolate(method='linear').iloc[:-1,:]

    wi_df = pd.DataFrame(index = Time, data = wi)
    wi_df_interp = wi_df.resample(freq).interpolate(method='linear').iloc[:-1,:]

    leveli_df = pd.DataFrame(index = Time, data = leveli)
    leveli_df_interp = leveli_df.resample(freq).interpolate(method='linear').iloc[:-1,:]

    data_interp = {}
    data_interp['t'] = ti_df_interp.values
    data_interp['q'] = qi_df_interp.values
    data_interp['u'] = ui_df_interp.values
    data_interp['v'] = vi_df_interp.values
    data_interp['w'] = wi_df_interp.values
    data_interp['level'] = leveli_df_interp.values
    data_interp['time'] = ti_df_interp.index
    data_interp['height_arr'] = height_arr
    return data_interp

def overlap_avors():
    overlapPath = 'F:/Avors/雷达重新解算方法与工具 - PKU/'
    overlapFilea = 'overlapPMT053.cpf'
    f = open(overlapPath+overlapFilea)
    lines = f.readlines()
    results = []
    for i in range(len(lines)):
        if lines[i].startswith('  <OverlapA>'):
            startIndxA = i + 1
        if lines[i].startswith('  </OverlapA>'):
            endIndxA = i - 1
        if lines[i].startswith('  <OverlapB>'):
            startIndxB = i + 1
        if lines[i].startswith('  </OverlapB>'):
            endIndxB = i - 1
        result = re.findall(r'<float>(.*?)</float>', lines[i])
        if result != []:
            result = float(result[0])
        results.append(result)
    overlapA = np.array(results[startIndxA:endIndxA + 1])
    overlapB = np.array(results[startIndxB:endIndxB + 1])
    d = {'overlapA': overlapA, 'overlapB': overlapB}
    hgt = np.arange(15,30015,15) #unit m
    overlapdf = pd.DataFrame(index = hgt, data = d)
    return overlapdf

def overlap_manual_fit(hgt, ovlp):
    '''
    用于实现手动拟合overlap函数，以MPL为参考，确定AVORS的overlap函数
    '''
    #Heightmpl = np.arange(1,2186+1)*15/1000
    #hgt = Heightmpl[13:2000]
    #ovlp_data = np.loadtxt(fname)
    #hgt = ovlp_data[:, 0]
    #ovlp = ovlp_data[:, 1]
    #hgt_pivot = np.array([hgt[6], hgt[26], hgt[46], hgt[66], 1.5])
    # 如果手动调整的overlap函数，在1.5km之上还没有为1，那么手动调整其为1。如果在1km到1.5km之前单调地穿过了x=1的线，那么赋值其在穿过那个点以及以上为1。

    hgt_pivot = np.append(hgt[np.array([6, 26, 46, 66])], 1.5)
    ovlp_pivot = np.array([ovlp[6], ovlp[26], ovlp[46], ovlp[66], 1])
    z = np.polyfit(hgt_pivot, ovlp_pivot, deg=4)
    p = np.poly1d(z)
    height = np.arange(15,30015,15)/1000
    #从0.3km 到 1.5km的overlap
    ovlp_fit_1 = p(hgt)

    overlapdf = overlap_avors()
    ovlp_pivot2 = np.append(overlapdf['overlapA'].loc[np.array([60, 90])].values, ovlp[np.array([6])])
    hgt_pivot2 = np.array([0.06, 0.09, 0.3])
    z2 = np.polyfit(hgt_pivot2, ovlp_pivot2, deg=2)
    p2 = np.poly1d(z2)
    #从0.015km 到 0.3km的overlap
    ovlp_fit_2 = p2(height)
    ovlp_fit = np.concatenate([ovlp_fit_2[:20], ovlp_fit_1[7:87], np.ones(np.sum(height>1.5))])
    return ovlp_fit

import glob
def overlap_avors_dynamic(day):
    # 用实际数据画图计算的方法得到动态的overlap，是日期的函数。每个月对应一个overlap函数
    # 不加以区分通道A和通道B的overlap函数。
    # 输出的ovlp_fit是 大小2000的矩阵，对应高度为height = np.arange(15,30015,15)/1000
    if day[:4] == '2022':
        day = day
    else:
        day = '20220101'
    ovlp_list = []
    ovlp_savepath = f'C:/Users/<USER>/Desktop/Alex/DepolarizationCalibration/code/overlap_files/{day[:6]}/'
    for fname in glob.glob(ovlp_savepath+'overlap*.txt'):
        ovlp_data = np.loadtxt(fname)
        ovlp_ = ovlp_data[:, 1]
        ovlp_list.append(ovlp_)
    ovlp = np.mean(np.array(ovlp_list), axis=0)   # 为手动测量值的平均数
    hgt = (np.arange(1,2186+1)*15/1000)[13:2000]
    ovlp_fit = overlap_manual_fit(hgt, ovlp)      # 为手动测量值的经过平滑和拟合的结果，用于下一步计算
    return ovlp_fit, ovlp

def ScanningMask():
    '''
    用于剔除不是75度高度的扫描数据，不进入下一步统计处理。
    '''
    pass

def Getlc(minute2average):
    '''
    从每半个小时一次的原始雷达常数数据得到平滑插值后的雷达常数数据
    '''
    lc_df_avors = pd.read_pickle(r'F:/Avors/code/calibration/lc_df_avors')
    lc_df_mpl = pd.read_pickle(r'E:/MPL/MPLcode/workfile/Rayleighfit/calibration/lc_df_mpl')
    lc_df_avors['lidarConstsmoothed'] = lc_df_avors['lidarConst'].rolling(window=5).mean()
    lc_df_mpl['lidarConstsmoothed'] = lc_df_mpl['lidarConst'].rolling(window=5).mean()

    start_date = '2022-01-01'
    end_date = '2023-01-01'
    freq = str(int(minute2average)) + 'T' 
    #freq = '5T'  # 5分钟
    time_index = pd.date_range(start=start_date, end=end_date, freq=freq)

    lc_df_AVORS = lc_df_avors.reindex(time_index)
    lc_df_AVORS['lidarConstsInter'] = lc_df_AVORS['lidarConstsmoothed'].interpolate(method='linear')
    lc_df_AVORS['lidarConstsInter'] = lc_df_AVORS['lidarConstsInter'].fillna(method='ffill').fillna(method='bfill')

    lc_df_MPL = lc_df_mpl.reindex(time_index)
    lc_df_MPL['lidarConstsInter'] = lc_df_MPL['lidarConstsmoothed'].interpolate(method='linear')
    lc_df_MPL['lidarConstsInter'] = lc_df_MPL['lidarConstsInter'].fillna(method='ffill').fillna(method='bfill')
    return lc_df_AVORS, lc_df_MPL

def CalAttBsc(day, minute2average=5):
    '''
    用来计算衰减的后向散射系数
    '''
    #signal_interp = interp_Raw(day=day,minute2average=minute2average)
    signal_interp = interp_Raw(day=day,minute2average=minute2average, deadtime=5)
    signal_sum = signal_interp['nrb']
    signal = mpl.average_mpl(day=day,minute2average=minute2average)
    signal_all_mpl = signal['signal_all']
    
    lc_df_AVORS, lc_df_MPL = Getlc(minute2average=minute2average)
    lc_avors = lc_df_AVORS['lidarConstsInter'].loc[day].values
    atten_beta_avors = signal_sum / lc_avors.reshape(-1, 1)
    
    lc_mpl = lc_df_MPL['lidarConstsInter'].loc[day].values
    atten_beta_mpl = signal_all_mpl / lc_mpl.reshape(-1, 1)
    return atten_beta_avors, atten_beta_mpl

def CalAttBscAVORS(day, minute2average=5):
    '''
    用来计算衰减的后向散射系数
    '''
    #signal_interp = interp_Raw(day=day,minute2average=minute2average)
    signal_interp = interp_Raw(day=day,minute2average=minute2average, deadtime=5)
    signal_sum = signal_interp['nrb']
    
    lc_df_AVORS, lc_df_MPL = Getlc(minute2average=minute2average)
    lc_avors = lc_df_AVORS['lidarConstsInter'].loc[day].values
    atten_beta_avors = signal_sum / lc_avors.reshape(-1, 1)

    return atten_beta_avors

def CalAttBscMPL(day, minute2average=5):
    '''
    用来计算衰减的后向散射系数
    '''
    signal = mpl.average_mpl(day=day,minute2average=minute2average)
    signal_all_mpl = signal['signal_all']
    
    lc_df_AVORS, lc_df_MPL = Getlc(minute2average=minute2average)
    
    lc_mpl = lc_df_MPL['lidarConstsInter'].loc[day].values
    atten_beta_mpl = signal_all_mpl / lc_mpl.reshape(-1, 1)
    return atten_beta_mpl

#---------------------------------------------------------------------------------#
#------------------------------    画图   ----------------------------------------#
#---------------------------------------------------------------------------------#
def PlotAttBscLidars(day, minute2average=5):
    '''
    画两台lidar的衰减后向散射系数
    '''
    #atten_beta_a, atten_beta_m = CalAttBsc(day, 5)
    date = dt.datetime.strptime(day, '%Y%m%d')

    fig, axs = plt.subplots(2, 1, figsize=(6, 8))
    height = np.arange(1,2186+1)*15/1000.
    norm = pylabmpl.colors.Normalize(vmin=0,vmax=50)
    thiscmap = cmaps.BlAqGrYeOrReVi200
    cmap=thiscmap
    cbar_ax = fig.add_axes([0.95, 0.15, 0.02, 0.7])  # 设置colorbar的位置和大小
    #cbar.set_label(r'$\mathbf{(Mm^{-1} sr^{-1})}$', fontsize=10)  # 设置colorbar标签
    cbar_ax.set_ylabel(r'$\mathbf{(Mm^{-1} sr^{-1})}$', fontsize=10)
    fig.suptitle(f'Attenuated Backscatter on {day}',fontsize=12, weight='semibold', y=0.92)
    
    try:
        signal_interp = interp_Raw(day=day,minute2average=minute2average)
        tim_a = signal_interp['time']
        start_date = dt.datetime(int(day[:4]),1,1)
        time_a = [start_date + dt.timedelta(days=day - 1) for day in tim_a]
        atten_beta_a = CalAttBscAVORS(day, minute2average=5)
        ax = axs[0]
        pcm = ax.pcolormesh(time_a, height[4:], atten_beta_a.T[4:, :], cmap=cmap, norm=norm)
        cbar = fig.colorbar(pcm, cax=cbar_ax)  # 创建colorbar
        ax.set_ylim(0, 14)

        t1 = 0; t2 = 24;
        TimeStart = dt.datetime(int(day[0:4]),int(day[4:6]),int(day[6:8]),t1,0,0)
        TimeEnd=TimeStart+dt.timedelta(hours=(t2-t1))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.set_xlim(TimeStart,TimeEnd)
        ax.set_ylabel('Height(km)',fontsize=10,weight='semibold')
        #ax.set_xlabel('Local Time',fontsize=10,weight='semibold')
        ax.set_title('Slant Lidar',fontsize=12,weight='semibold',y=0.99)
    except:
        pass
    try:
        signal = mpl.average_mpl(day=day,minute2average=minute2average)
        time_mpl = signal['time']
        time_m=time_mpl*dt.timedelta(hours=1)+date
        atten_beta_m = CalAttBscMPL(day, minute2average=5)
        ax1 = axs[1]
        pcm1 = ax1.pcolormesh(time_m, height[13:], atten_beta_m.T[13:, :], cmap=cmap, norm=norm)
        cbar = fig.colorbar(pcm1, cax=cbar_ax)  # 创建colorbar
        ax1.set_ylim(0, 14)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax1.set_xlim(TimeStart,TimeEnd)

        ax1.set_ylabel('Height(km)',fontsize=10,weight='semibold')
        ax1.set_xlabel('Local Time',fontsize=10,weight='semibold')
        ax1.set_title('Zenith Lidar',fontsize=12,weight='semibold',y=1)
    except:
        pass
    
    return fig

def PlotAttBscDepLidars(day, minute2average,logFlag=0,t1=0,t2=24,hmin=0,hmax=14):
    '''
    绘制两台雷达既有衰减后向散射系数，又有退偏比的图片
    '''
    height = np.arange(1,2186+1)*15/1000.
    #logFlag = 0 ###修改这里来控制是不是用log坐标
    logSave = 'log' if logFlag == 1 else ''

    # hmin=0; hmax=14
    # t1=0; t2=24
    majorinterval=3
    minorinterval=1
    fontsize = [34,25,20,30]

    sday = day
    thiscmap = cmaps.BlAqGrYeOrReVi200
    blind=13; vmin1 = 0;vmax1=50;vmin2=0;vmax2=0.5;vmin1log=10**-1;vmax1log=10**3

    fig = plt.figure(figsize=(28,18))
    plt.suptitle('Lidar Observation on '+sday[0:4]+'/'+sday[4:6]+'/'+sday[6:8],fontsize=fontsize[0],weight='semibold')
    
    norm3 = pylabmpl.colors.Normalize(vmin=vmin1,vmax=vmax1)
    norm3log = pylabmpl.colors.LogNorm(vmin=vmin1log, vmax=vmax1log)
    try:
    #----------Avors-----------#    
        atten_beta_a = CalAttBscAVORS(day, minute2average)
        signal_interp = interp_Raw(day=day,minute2average=minute2average, deadtime=5)
        #signal_interp = interp_Raw(day=day,minute2average=minute2average)
        tim_a = signal_interp['time']
        start_date = dt.datetime(int(day[:4]),1,1)
        time_a = [start_date + dt.timedelta(days=day - 1) for day in tim_a]
        depolarization = signal_interp['depolarization']
        k_star =  0.9534068811205065
        g = 0.03299804403834469
        depolarization = depolarization / k_star - g
        
        ax3 = fig.add_axes((0.54, 0.5, 0.38, 0.40))
        bar3 = fig.add_axes((0.93,0.5,0.015,0.3))

        ax4 = fig.add_axes((0.54, 0.06, 0.38, 0.40))
        plt.yticks(np.arange(0,15,1),fontsize=fontsize[2],weight='semibold')
        plt.xticks(fontsize=fontsize[2])
        
        if logFlag == True:
            ax3.pcolormesh(time_a,height[13:],atten_beta_a.T[13:,:],cmap=thiscmap,norm=norm3log)#,vmin=vmin1log,vmax=vmax1log,
            colorbar3 = pylabmpl.colorbar.ColorbarBase(bar3,cmap=thiscmap,norm=norm3log,orientation='vertical')
        elif logFlag == False:
            ax3.pcolormesh(time_a,height[13:],atten_beta_a.T[13:,:],cmap=thiscmap,norm=norm3)
            colorbar3 = pylabmpl.colorbar.ColorbarBase(bar3,cmap=thiscmap,norm=norm3,orientation='vertical')
        #ax3.set_ylim(hmin,hmax)
        ax4.pcolormesh(time_a,height[13:],depolarization.T[13:,:],vmin=vmin2,vmax=vmax2,cmap=thiscmap)
        #ax4.set_ylim(hmin,hmax)
        
        ax3.set_yticks(np.arange(0, hmax+1, 1))
        ax3.set_yticklabels(ax3.get_yticks(), fontsize=fontsize[2], weight='semibold')
        ax3.set_xticklabels(ax3.get_xticks(), fontsize=fontsize[2])#, weight='semibold')
        ax3.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
        ax3.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        # TimeStart = dt.datetime(int(sday[0:4]),int(sday[4:6]),int(sday[6:8]),0,0,0)
        # TimeEnd=parse(sday)+dt.timedelta(days=1)
        TimeStart = dt.datetime(int(sday[0:4]),int(sday[4:6]),int(sday[6:8]),t1,0,0)
        TimeEnd=TimeStart+dt.timedelta(hours=(t2-t1))
        ax3.set_xlim(TimeStart,TimeEnd)
        ax3.text(TimeStart+dt.timedelta(hours=(t2-t1))*3.5/24, (hmax-hmin)*15/14+hmin,'Slant-Pointed Polarization Lidar', fontsize=fontsize[3],weight='semibold')
        
        ax4.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
        ax4.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax4.set_xlim(TimeStart,TimeEnd)
        
        #colorbar.set_label(r'$\mathsf{\mathbf{counts\cdot km^2/(\mu s\cdot \mu J)}}$',fontsize=fontsize[1],weight='semibold')
        colorbar3.set_label(r'$\mathbf{(Mm^{-1} sr^{-1})}$',fontsize=fontsize[1],weight='semibold')
        for tick in colorbar3.ax.get_yticklabels(): 
            tick.set_fontsize(fontsize[2])
            tick.set_weight('semibold')
        
        bar4 = fig.add_axes((0.93,0.06,0.015,0.3))
        norm = pylabmpl.colors.Normalize(vmin=vmin2,vmax=vmax2)
        colorbar = pylabmpl.colorbar.ColorbarBase(bar4,cmap=thiscmap,norm=norm,orientation='vertical')
        for tick in colorbar.ax.get_yticklabels(): 
            tick.set_fontsize(fontsize[2])
            tick.set_weight('semibold')
        
        ax4.set_xlabel('Local Time',fontsize=fontsize[1],weight='semibold')
        ax3.set_ylabel('Height(km)',fontsize=fontsize[1],weight='semibold')
        ax4.set_ylabel('Height(km)',fontsize=fontsize[1],weight='semibold')
        #ax1.set_title('Lidar Observation on '+day[0:4]+'/'+day[4:6]+'/'+day[6:8],fontsize=fontsize[0],weight='semibold',pad=10)
        #plt.title('Attenuated Backscattering Coefficient',fontsize=fontsize[1],weight='semibold')
        ax3.set_title('Attenuated Backscattering Coefficient',fontsize=fontsize[1],weight='semibold')
        ax4.set_title('Linear Depolarization Ratio',fontsize=fontsize[1],weight='semibold')
        
        set_axis(ax3,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
        set_axis(ax4,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
        ax3.set_ylim(hmin,hmax)
        ax4.set_ylim(hmin,hmax)
    except:
        print('Something wrong in AVORS plot')
        pass
    try:
    #--------------MPL------------#
        signal = mpl.average_mpl(day=day,minute2average=minute2average)
        atten_beta_m = CalAttBscMPL(day, minute2average)
        time_mpl = signal['time']
        #time_m=time_mpl*dt.timedelta(hours=1)+time_
        date = dt.datetime.strptime(day, '%Y%m%d')
        time_m=time_mpl*dt.timedelta(hours=1)+date
        #print(time)
        height_mpl = signal['height']
        #signal_all_mpl = signal['signal_all']
        #atten_beta_mpl = signal_all_mpl / 0.38
        depolarization_mpl = signal['depolarization']
        #signal_all_mpl = np.ma.masked_where(np.isnan(signal_all_mpl),signal_all_mpl)
        depolarization_mpl = np.ma.masked_where(np.isnan(depolarization_mpl),depolarization_mpl)
        depolarization_mpl = depolarization_mpl - 0.02

        ax1 = fig.add_axes((0.05,0.5,0.38,0.40))
        bar1 = fig.add_axes((0.44,0.5,0.015,0.3))
        plt.yticks(np.arange(0,15,1),fontsize=fontsize[2],weight='semibold')
        plt.xticks(fontsize=fontsize[2])
        ax2 = fig.add_axes((0.05,0.06,0.38,0.40))
        plt.yticks(np.arange(0,15,1),fontsize=fontsize[2],weight='semibold')
        plt.xticks(fontsize=fontsize[2])
        
        norm1 = pylabmpl.colors.Normalize(vmin=vmin1,vmax=vmax1)
        norm1log = pylabmpl.colors.LogNorm(vmin=vmin1log, vmax=vmax1log)
        if logFlag == True:
            ax1.pcolormesh(time_m,height_mpl[blind:],atten_beta_m.T[blind:,:],cmap=thiscmap,norm=norm1log)
            colorbar1 = pylabmpl.colorbar.ColorbarBase(bar1,cmap=thiscmap,norm=norm1log,orientation='vertical')    
        elif logFlag == False:
            ax1.pcolormesh(time_m,height_mpl[blind:],atten_beta_m.T[blind:,:],cmap=thiscmap,norm=norm1)
            colorbar1 = pylabmpl.colorbar.ColorbarBase(bar1,cmap=thiscmap,norm=norm1,orientation='vertical')    
        #ax1.set_xlim(t1,t2)
        #ax1.set_ylim(hmin,hmax)
        ax2.pcolormesh(time_m,height_mpl[blind:],depolarization_mpl.T[blind:,:],vmin=vmin2,vmax=vmax2,cmap=thiscmap)
        #ax2.set_xlim(t1,t2)
        #ax2.set_ylim(hmin,hmax)
        
        ax1.set_yticks(np.arange(0, hmax+1, 1))
        ax1.set_yticklabels(ax1.get_yticks(), fontsize=fontsize[2], weight='semibold')
        ax1.set_xticklabels(ax1.get_xticks(), fontsize=fontsize[2])#, weight='semibold')
        ax1.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
        ax1.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        TimeStart = dt.datetime(int(sday[0:4]),int(sday[4:6]),int(sday[6:8]),t1,0,0)
        #TimeEnd=parse(sday)+dt.timedelta(days=1)
        TimeEnd=TimeStart+dt.timedelta(hours=(t2-t1))
        ax1.set_xlim(TimeStart,TimeEnd)
        ax1.text(TimeStart+dt.timedelta(hours=(t2-t1))*3.6/24, (hmax-hmin)*15/14+hmin,'Zenith-Pointed Polarization Lidar', fontsize=fontsize[3],weight='semibold')
        
        ax2.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
        ax2.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax2.set_xlim(TimeStart,TimeEnd)
        
        #colorbar.set_label(r'$\mathsf{\mathbf{counts\cdot km^2/(\mu s\cdot \mu J)}}$',fontsize=fontsize[1],weight='semibold')
        colorbar1.set_label(r'$\mathbf{(Mm^{-1} sr^{-1})}$',fontsize=fontsize[1],weight='semibold')
        for tick in colorbar1.ax.get_yticklabels(): 
            tick.set_fontsize(fontsize[2])
            tick.set_weight('semibold')
        
        bar2 = fig.add_axes((0.44,0.06,0.015,0.3))
        norm = pylabmpl.colors.Normalize(vmin=vmin2,vmax=vmax2)
        colorbar = pylabmpl.colorbar.ColorbarBase(bar2,cmap=thiscmap,norm=norm,orientation='vertical')
        for tick in colorbar.ax.get_yticklabels(): 
            tick.set_fontsize(fontsize[2])
            tick.set_weight('semibold')
        
        ax2.set_xlabel('Local Time',fontsize=fontsize[1],weight='semibold')
        ax1.set_ylabel('Height(km)',fontsize=fontsize[1],weight='semibold')
        ax2.set_ylabel('Height(km)',fontsize=fontsize[1],weight='semibold')
        #ax1.set_title('Lidar Observation on '+day[0:4]+'/'+day[4:6]+'/'+day[6:8],fontsize=fontsize[0],weight='semibold',pad=10)
        #plt.title('Attenuated Backscattering Coefficient',fontsize=fontsize[1],weight='semibold')
        ax1.set_title('Attenuated Backscattering Coefficient',fontsize=fontsize[1],weight='semibold')
        ax2.set_title('Linear Depolarization Ratio',fontsize=fontsize[1],weight='semibold')
        
        set_axis(ax1,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
        set_axis(ax2,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
        ax1.set_ylim(hmin,hmax)
        ax2.set_ylim(hmin,hmax)
    except:
        pass
    #fig.savefig(f'{savepath}/combine_{day}_{t1}to{t2}_{minute2average}min_mean_hmin_{hmin}_hmax_{hmax}{logSave}.png')
    return fig, logSave

def PlotAttBscDepRatioLidars(signal_interp, signal, day, t1, t2, hmin, hmax):
    #----------------画beta'之比与退偏比之比-------------------#
    #t1=0; t2=24
    majorinterval=3
    minorinterval=1
    #hmin=0; hmax=14

    fig = plt.figure(figsize=(7,10))
    ax = fig.add_axes((0.07,0.5,0.80,0.4))
    plt.yticks(np.arange(0,15,1),fontsize=10,weight='semibold')
    plt.xticks(fontsize=10)

    height = np.arange(1,2186+1)*15/1000.
    thiscmap = cmaps.BlAqGrYeOrReVi200
    norm = pylabmpl.colors.Normalize(vmin=0,vmax=5)

    #signal_interp = interp_Raw(day=day,minute2average=5)
    #signal = mpl.average_mpl(day=day,minute2average=5)
    dep = signal_interp['depolarization'][:, 13:1000]
    dep_mpl = signal['depolarization'][:, 13:1000]
    tim = signal_interp['time']
    start_date = dt.datetime(int(day[:4]),1,1)
    time = [start_date + dt.timedelta(days=day - 1) for day in tim]

    #nrb = signal_interp['nrb'][:, 13:1000]
    #nrb_mpl = signal['signal_all'][:, 13:1000]

    atten_beta_a = CalAttBscAVORS(day, 5)
    atten_beta_m = CalAttBscMPL(day, 5)
    
    betaPrimeratio = (atten_beta_m / atten_beta_a)[:, 13:1000]
    depratio = dep_mpl/dep

    ax.pcolormesh(time,height[13:1000], betaPrimeratio.T[:,:],cmap=thiscmap,norm=norm)
    ax.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
    ax.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax.set_title(f'Attenuated Backscatter Coefficient Ratio', 
                    weight='semibold')

    TimeStart = dt.datetime(int(day[0:4]),int(day[4:6]),int(day[6:8]),t1,0,0)
    TimeEnd=TimeStart+dt.timedelta(hours=(t2-t1))
    ax.set_xlim(TimeStart,TimeEnd)
    ax.set_ylim(hmin,hmax)
    #ax.set_xlabel('Local Time',fontsize=10, weight='semibold')
    ax.set_ylabel('Height (km)', fontsize=10, weight='semibold')

    bar = fig.add_axes((0.9,0.5,0.02,0.3))
    cbar = pylabmpl.colorbar.ColorbarBase(bar,cmap=thiscmap,norm=norm,orientation='vertical')
    cbar.ax.set_title(r'$\mathbf{\frac{\ \mathtt{\beta}_{zenith}^{\prime}}{\mathtt{\beta}_{slant}^{\prime}}}$',pad=20, fontsize=14)

    #fig.savefig(f'H:/Avors/code/HOIC/depRatio{day}_{hmax}.png',bbox_inches='tight')
    norm1 = pylabmpl.colors.Normalize(vmin=0,vmax=2)
    ax1 = fig.add_axes((0.07,0.05,0.80,0.4))
    plt.yticks(np.arange(0,15,1),fontsize=10,weight='semibold')
    plt.xticks(fontsize=10)
    ax1.pcolormesh(time,height[13:1000], depratio.T[:,:],cmap=thiscmap,norm=norm1)
    ax1.set_xlim(TimeStart,TimeEnd)
    ax1.set_ylim(hmin,hmax)
    ax1.set_xlabel('Local Time',fontsize=10, weight='semibold')
    ax1.set_ylabel('Height (km)', fontsize=10, weight='semibold')

    ax1.xaxis.set_major_locator(mdates.HourLocator(interval = majorinterval))
    ax1.xaxis.set_minor_locator(mdates.HourLocator(interval = minorinterval))
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax1.set_title(f'Volume Depolarization Ratio', 
                    weight='semibold')

    bar1 = fig.add_axes((0.9,0.05,0.02,0.3))
    cbar1 = pylabmpl.colorbar.ColorbarBase(bar1,cmap=thiscmap,norm=norm1,orientation='vertical')
    cbar1.ax.set_title(r'$\mathbf{\frac{\ \mathtt{\delta}_{zenith}^{\prime}}{\mathtt{\delta}_{slant}^{\prime}}}$',pad=20, fontsize=14)
    fig.suptitle(f'Zenith and Slant Pointed Lidars\' Ratio on {day}', y=0.94,fontsize=15, weight='semibold')
    
    return fig




# def CalDiameter(vt, P, T, D):
#     '''
#     JQSRT Yun He, 2021 Horizontally oriented ice crystals observed by the synergy... 
#     Calculate Reynolds number
#     先解出D，再计算雷诺数
#     '''

#     P0 = 1031.25 #hPa
#     B = 110.4 # K  gas-type-related constant
#     eta0 = 1.7894e-5 # Pa s
#     g = 9.8 # m / s**2
#     rho_ice = 917.0 # kg/ m**2
#     delta0 = 8
#     C0 = 0.35

#     m = (3 * np.sqrt(3) / 8) * D ** 2 * (0.04 * D) * rho_ice 
#     rho_air = 1.293 * P / P0 * 273.15 / T
#     eta = eta0 * ((T / 288.15) ** 1.5) * (288.15 + B) / (T + B)
#     X = (rho_air / eta ** 2) * ((8 * m * g) / (np.pi * 0.827 ** 0.5))
    
#     term1 = np.sqrt(1 + 4 * np.sqrt(X) / (delta0 ** 2) / np.sqrt(C0))
#     Re = (delta0 ** 2) / 4 * (term1 - 1) ** 2
    
#     return  eta * Re / (rho_air * vt) - D

# def CalReynolds(vt, P, T):
#     return




# StartTime = dt.datetime(2022,12,2)   #change this line
# EndTime = dt.datetime(2022,12,2)
# d= (EndTime-StartTime).days+1
# for i in range(d): #range(2) is 0,1
#     try:
#         time_ = StartTime + dt.timedelta(days = i)
#         day = time_.strftime('%Y%m%d')
#         #day=20220711
#         sday=str(day)
#         path='H:/Avors/AvorsData/'
        
#         filename='Channel A Square of Range_'
#         filenameb='Channel B Square of Range_'
#         #data=pd.read_csv(path+filename+sday+'.csv',encoding='gbk',index_col='时间/高度(m)')
#         #datab=pd.read_csv(path+filenameb+sday+'.csv',encoding='gbk',index_col='时间/高度(m)')
#         if time_<dt.datetime(2022,11,21,0,0) and time_>dt.datetime(2022,1,10,0,0):
#             data=pd.read_csv(path+filename+sday+'.csv',encoding='gbk',index_col='时间/高度(m)')
#             datab=pd.read_csv(path+filenameb+sday+'.csv',encoding='gbk',index_col='时间/高度(m)')
#         else:
#             data=pd.read_csv(path+filename+sday+'.csv',encoding='gbk',index_col='时间/距离(M)')
#             datab=pd.read_csv(path+filenameb+sday+'.csv',encoding='gbk',index_col='时间/距离(M)')        
#         data.index=pd.to_datetime(data.index)        
#         datab.index=pd.to_datetime(datab.index)
#         minute2average=5      # 几分钟平均
#         if minute2average==1:
#             data_5mean=data
#             datab_5mean=datab
#         else:
#             data_5mean=data.resample(str(minute2average)+'T').mean()
#             datab_5mean=datab.resample(str(minute2average)+'T').mean()
#         height=np.arange(15,30015,15)/1000
#         height=height[3:]*math.sin(math.radians(75))
#         time=data_5mean.index
#         np.save('./SWC/time_'+sday+'.npy',time)
#         cha=data_5mean.values[:,3:]
#         chb=datab_5mean.values[:,3:]
#         nrb=cha+chb
#         depolarization=chb/cha
#         signal_sum=cha+chb
#         #%%
#         vmax=5
#         vmax_d=0.3
#         vmin=0
#         hmin=0
#         hmax=14
#         fontsize = [30,25,20]
#         fig = plt.figure(figsize=(16,18))
#         plt.suptitle('Avors Slant Lidar Observation on '+sday[0:4]+'/'+sday[4:6]+'/'+sday[6:8],fontsize=fontsize[0],weight='semibold')
#         ax1 = fig.add_axes((0.07,0.5,0.80,0.40))
#         plt.ylim(0,hmax)
#         plt.xticks(fontsize=fontsize[2])
#         plt.yticks(np.arange(0,15,1),fontsize=fontsize[2],weight='semibold')
#         #plt.yticks(fontsize=fontsize[2],weight='semibold') 
#         plt.ylabel('Height(km)',fontsize=fontsize[1],weight='semibold')
#         plt.title('Normalized Relative Backscatter',fontsize=fontsize[1],weight='semibold')
#         bar = fig.add_axes((0.9,0.5,0.03,0.3))
#         vmin1 = 0.
#         vmax1 = vmax
#         norm = pylabmpl.colors.Normalize(vmin=vmin1,vmax=vmax1)
#         colorbar = pylabmpl.colorbar.ColorbarBase(bar,cmap=cmaps.BlAqGrYeOrReVi200,norm=norm,orientation='vertical')
#         colorbar.set_label(r'$\mathsf{\mathbf{counts\cdot km^2/(\mu s\cdot \mu J)}}$',fontsize=fontsize[0],weight='semibold')
#         for tick in colorbar.ax.get_yticklabels(): 
#             tick.set_fontsize(fontsize[2])
#             tick.set_weight('semibold')
#         ax1.xaxis.set_major_locator(mdates.HourLocator(interval = 3))
#         ax1.xaxis.set_minor_locator(mdates.HourLocator(interval = 1))
#         ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
#         TimeStart = dt.datetime(int(sday[0:4]),int(sday[4:6]),int(sday[6:8]),0,0,0)
#         TimeEnd=parse(sday)+dt.timedelta(days=1)
#         ax1.set_xlim(TimeStart,TimeEnd)
#         #ax1.xaxis.set_minor_locator(MultipleLocator(1))
#         ax2 = fig.add_axes((0.07,0.06,0.80,0.40))
#         plt.ylim(0,hmax)
#         #plt.xticks(np.arange(0.,25.,3),('00','03','06','09','12','15','18','21','24'),fontsize=fontsize[2],weight='semibold')
#         plt.yticks(np.arange(0,15,1),fontsize=fontsize[2],weight='semibold')
#         plt.xlabel('Local Time',fontsize=fontsize[1],weight='semibold')
#         plt.ylabel('Height(km)',fontsize=fontsize[1],weight='semibold')
#         plt.title('Linear Depolarization Ratio',fontsize=fontsize[1],weight='semibold')
#         plt.xticks(fontsize=fontsize[2])
#         ax2.xaxis.set_major_locator(mdates.HourLocator(interval = 3))
#         ax2.xaxis.set_minor_locator(mdates.HourLocator(interval = 1))
#         ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
#         bar = fig.add_axes((0.9,0.06,0.03,0.3))
#         vmin2 = 0.
#         vmax2 = vmax_d
#         norm = pylabmpl.colors.Normalize(vmin=vmin2,vmax=vmax2)
#         colorbar = pylabmpl.colorbar.ColorbarBase(bar,cmap=cmaps.BlAqGrYeOrReVi200,norm=norm,orientation='vertical')
#         for tick in colorbar.ax.get_yticklabels(): 
#             tick.set_fontsize(fontsize[2])
#             tick.set_weight('semibold')
#         set_axis(ax1,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
#         set_axis(ax2,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
#         ax2.set_xlim(TimeStart,TimeEnd)
#         ax1.pcolormesh(time,height,signal_sum.T,vmin=vmin,vmax=vmax,cmap=cmaps.BlAqGrYeOrReVi200)
#         ax2.pcolormesh(time,height,depolarization.T,vmin=vmin,vmax=vmax_d,cmap=cmaps.BlAqGrYeOrReVi200)
#         fig.savefig('./Pics/combine_'+str(day)+'_'+str(minute2average)+'min_mean'+'.png')
#         #%%
#         sounding = ReadRadiosonde('E:/MPL/54511探空资料/data/Beijing_'+str(day)+'_00.radiosonde')
#         height1 = sounding['HGHT']/1000.
#         T1 = sounding['TEMP']
#         sounding2 = ReadRadiosonde('E:/MPL/54511探空资料/data/Beijing_'+str(day)+'_12.radiosonde')
#         height2 = sounding2['HGHT']/1000.
#         T2 = sounding2['TEMP']
#         T3 = np.interp(height,height1,T1)
#         T4 = np.interp(height,height2,T2)
#         T=0.5*(T3+T4)
#         #%%
#         Nbin = len(height)
#         Ntime = len(time)
#         z = height
#         Nz = len(z)
#         F_list=[]
#         F2_list=[]
    
#         cloud = np.zeros([Ntime,Nz])
#         for itime in range(Ntime):
#                 # P is mpl backscatter signal without range correction
#                #print time[itime]
#             P = nrb[itime]/height**2.
#             PM = nrb[itime]
#                 # calculate noise
#             Pnoise = P[height>=15.]
#             with warnings.catch_warnings():
#                 warnings.simplefilter("ignore", category=RuntimeWarning)
#                 sd = np.nanstd(Pnoise,ddof=1)
#             if np.isnan(sd):
#                 cloud[itime,:] = np.nan
#                 continue
#             k=6
#             noise = k*sd
#             #print noise
                
#             # removing noise
#             d = depolarization[itime]
#             maxheight=12
#             P = P[height<=maxheight]
#             #Nz=len(P)
#             Ps = smooth.moving_average(P,3)
#             PD1 = Ps[:]
#             PD2 = Ps[:]
#             Nz=len(P)
#             for zi in range(Nz-1):
#                 if not abs(PD1[zi+1]-PD2[zi])>=noise:
#                     PD1[zi+1] = PD1[zi]
#                 if not abs(PD2[Nz-zi-2]-PD2[Nz-zi-1])>=noise:
#                     PD2[Nz-zi-2] = PD2[Nz-zi-1]
#             PD = (PD1+PD2)/2.
#             #plt.plot(PD,z)
#             #plt.show()
            
#             # ascending order
#             Is = np.argsort(PD)
#             Rs = PD[Is]
#             MA = np.nanmax(Rs)
#             MI = np.nanmin(Rs)
#             PE = np.linspace(1,Nz,Nz)/Nz
#             for i in range(Nz-1):
#                 if Rs[i]==Rs[i+1]:
#                     PE[i+1]=PE[i]
#             y = PE*(MA-MI)+MI
#             PN = np.zeros(Nz)
#             PN[Is] = y  #PN是直方图均衡化后的信号
            
#             # baseline
#             B = np.linspace(Nz,1,Nz)/Nz*(MA-MI)+MI
#             base = []
#             top = []
#             Area = []
#             F = []
#             F2 = []
#             d1 = []
#             d2 = []
#             DP = []
#             #DPF = []
#             J = []
#             #F1 = 100*(PN-B)/MA
                
#             #for zi in range(Nz-1):
#             #        d1.append((d[zi+1]-d[zi])/(z[zi+1]-z[zi]))
#             for zi in range(Nz-1) :
#                 if PN[zi+1]>B[zi+1] and PN[zi]<=B[zi]:
#                     s = 0.
#                     if PN[zi+1]>PN[zi]:
#                     #print z[zi+1]
#                         s = PN[zi+1]-B[zi+1]
#                     for i in range(Nz-zi-3):
#                         if PN[zi+i+2]>B[zi+i+2]: #and not(PN[zi+i+2]==PN[zi+i+1]):
#                             s = s+PN[zi+i+2]-B[zi+i+2]
        
#                         if PN[zi+i+2]<=B[zi+i+2]:
#                             if i+1>=3.:
#                                 base.append(zi+1)
#                                 top.append(zi+i+2)
#                                 if s<=(B[zi+1]-B[zi+i+2])*(i+1)/2.:
#                                     s = 0.
#                                 Area.append(s)
#                             break
#                 #注意这里的缩进是不是有必要
#                 Nlayer = len(base)
#                 #print base
#                 #print top
#                 #print Area
#                 # log slope
#                 #ln_nrb = np.log(PN*z**2.)
        
#                 #F = np.zeros(Nz)+np.nan
#                 #for zi in range(Nz-1):
#                 #    d1.append((d[zi+1]-d[zi])/(z[zi+1]-z[zi]))
#                     #F[zi] = (ln_nrb[zi+1]-ln_nrb[zi])/(z[zi+1]-z[zi])
#                 #print d1 
#                 #print('zi',zi)
#                 #print('itime',itime)
#                 for ilayer in range(Nlayer):
#                     F = []
#                     F1 = []
#                     F2 = []
#                     d2 = []
#                     DP = []
#                     DPF = []
#                     J = []
#                     F1 = 100*(PN-B)/MA
#                 #F = Area[ilayer]*z[base[ilayer]]**2./(z[top[ilayer]]-z[base[ilayer]])   
#                     PMX = 0.
#                     for i in range(base[ilayer]):
#                         PMX = PMX+PM[i]
#                 #print PMX
#                     F = Area[ilayer]*PMX/(z[top[ilayer]]-z[base[ilayer]])/MA
#                 #print F
#                     F2 = np.nanmax(F1[base[ilayer]:top[ilayer]+1])
#                 #     for j in range(base[ilayer],top[ilayer]+1):
#                 #         #F1.append(100*(PN-B)/MA)
#                 #         if d[j]<0.10:
#                 #             DP.append(d1[j])
#                 #             J.append(j)
                            
#                 #         #if d[j]<0.05:
#                 #         #    d2.append(j)
#                 #     #F2 = np.nanmax(F1[base[ilayer]:top[ilayer]+1])
#                 #     #print d
#                 #     #print DP
#                 #     #print J
#                 #     if len(DP)>0:
#                 #         #js = (np.argmin(J)+np.argmax(J))/2
#                 #         ks = np.argmax(J)
#                 #         ls = np.argmin(J)
#                 #         js = int(np.floor((ks+ls)/2))
#                 # #DPF = np.argmean(DP[d1[js]:d1[ks]])
#                 #         DPF = np.nanmean(DP[js:ks-1])
#                     #DPF = (d[J[ks]]-d[J[js]])/(z[J[ks]]-z[J[js]])
#                 #print d[top[ilayer]]
#                 #print DPF
#                 #print z[top[ilayer]]
#                     #print ls
#                     #print ks
#                     #print js
#                     #print z[base[ilayer]+ls]
#                     #print z[base[ilayer]+ks]
#                     #print z[base[ilayer]+js]
#                     #print d2
#                     #print DP
#                         #DPF = np.nanmean(DP[js:ks-1])
                    
                    
#                 #print (d[top[ilayer]-2]-DP)/(z[top[ilayer]-2]-z[Js])
#                 #DP = np.nanmean(d[base[ilayer]:top[ilayer]+1]) 
#                 #print F2"""
#                 #if z[base[ilayer]]<3.:
#                 #if z[base[ilayer]]<5.:
#                 #    if np.nanmax(F[base[ilayer]:top[ilayer]+1])>3. or np.nanmin(F[base[ilayer]:top[ilayer]+1])<-7.:  
#                 #    if Area[ilayer]*z[base[ilayer]]/(z[top[ilayer]]-z[base[ilayer]])>500.:
#                     #print('F',F)
#                     F_list.append(F)
#                     #print('F2',F2)
#                     F2_list.append(F2)
#                     F_threshold=2000.
#                     if F>F_threshold or F2>8.:
#                         cloud[itime,base[ilayer]:top[ilayer]+1] = 1. #水云
#                         #print d
#                         #print DPF
#                         #print T
#                         #print len
#                         for k in range(base[ilayer],top[ilayer]+1):
#                             if d[k]<=0.05 and T[k]<0 and T[k]>-38 and PM[k]>0.3:# and 0.1<DPF 
#                                 cloud[itime,k] = 2.     #过冷水云
#                                 #print(2)
#                             #if d[k]<=0.05 and T[k]<0 and T[k]>-38 and PM[k]>0.3:#and DPF<=0.1 
#                             #    cloud[itime,k] = 2.
#                             #    print(2)
#                             elif d[k]>0.15: #冰云
#                                 cloud[itime,k] = 4.
#                                 #print(4)
#                             elif T[k]<-38:             #冰云
#                                 cloud[itime,k] = 4.   
#                                 #print(4)
#                             #elif d[k]<=0.05 and T[k]>0: #水云
#                             #    cloud[itime,k] = 1.   
#                             #    print(1)
#                             elif T[k]<0:
#                                 cloud[itime,k] = 5.    #混合云
#                                 #print(5)
#                         #if cloud[itime]==2.:
#                         #    glstime.append(itime)  
#                 """for ilayer in range(Nlayer):
#                     T = np.nanmax(F[base[ilayer]:top[ilayer]+1])
#                     D = np.nanmin(F[base[ilayer]:top[ilayer]+1])
#                     if z[base[ilayer]]<3.:
#                         if T>3. or D<-7.:
#                             cloud[itime,base[ilayer]:top[ilayer]+1] = 1.
#                     else:
#                         if T>1. or D<-7.:
#                             #cloud[itime,base[ilayer]:top[ilayer]+1] = 1."""
#                 #plt.plot(z,cloud[itime])
#                 #plt.show()
#             #cloud_mask = np.ma.masked_where(cloud==0,cloud)
#         cloud[cloud==0]=np.nan
#         cloud_mask = np.ma.masked_where(np.isnan(cloud),cloud)
        
#         #print cloud_mask
#         #%%
#         fig = plt.figure(figsize=(16,9))
#         ax = fig.add_axes((0.07,0.10,0.8,0.85)) #左、底、宽、高
#         #plt.xlim(0,24)
#         ax.set_title('Avors Slant Lidar CloudMask '+sday[0:4]+'/'+sday[4:6]+'/'+sday[6:8],fontsize=fontsize[0],weight='semibold')
#         plt.ylim(0,14)
#         plt.xticks(fontsize=fontsize[2])
#         ax.xaxis.set_major_locator(mdates.HourLocator(interval = 3))
#         ax.xaxis.set_minor_locator(mdates.HourLocator(interval = 1))
#         ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
#         set_axis(ax,axis_lw = 2,ymajor = 1,yminor = 0.5,major_length =5,major_width =2,minor_length = 3)
#         plt.yticks(fontsize=25,weight='semibold')
#         ax.set_xlabel('Local Time',fontsize=fontsize[1],weight='semibold')
#         ax.set_ylabel('Height(km)',fontsize=30,weight='semibold')
#         colorlevel=[0.5,1.5,2.5,4.5,5.5]
#         colordict=['#000080','#0081FF','#FF9400','#800000']
#         cmap_=mcolors.ListedColormap(colordict)#产生颜色映射
#         norm_=mcolors.BoundaryNorm(colorlevel,cmap_.N)#生成索引
#         #norm=mcolors.Normalize(vmin=-0.5,vmax=cmap.N-0.5)
#         #plt.title('20160504',fontsize=25,weight='semibold')
#         ax.pcolormesh(time,z,cloud_mask.T,norm=norm_,cmap=cmap_)
#         ax.set_xlim(TimeStart,TimeEnd)
        
#         time1=pd.date_range(sday[:4]+sday[4:6]+sday[6:]+' 07:45',sday[:4]+sday[4:6]+sday[6:]+' 08:15',freq='5T')
#         time2=pd.date_range(sday[:4]+sday[4:6]+sday[6:]+' 19:45',sday[:4]+sday[4:6]+sday[6:]+' 20:15',freq='5T')
#         if max(T1)>0:
#             ax.plot(time1,np.ones(7)*height1[abs(T1)==min(abs(T1))][0],lw=2,color='k',label='0°C')
#         if max(T2)>0:
#             ax.plot(time2,np.ones(7)*height2[abs(T2)==min(abs(T2))][0],lw=2,color='k')
#         ax.plot(time1,np.ones(7)*height1[abs(T1+38)==min(abs(T1+38))][0],lw=2,color='r',label='-38°C')
#         ax.plot(time2,np.ones(7)*height2[abs(T2+38)==min(abs(T2+38))][0],lw=2,color='r')
#         ax.legend(fontsize=15,title='Sounding T',title_fontsize=18)
#         ax.annotate(r'$T_{sf1}=$'+str(T1[0])+'°C',(time_+dt.timedelta(hours=8,minutes=30),0.5),fontsize=15)
#         ax.annotate(r'$T_{sf2}=$'+str(T2[0])+'°C',(time_+dt.timedelta(hours=20,minutes=30),0.5),fontsize=15)
       
#         ax_ =fig.add_axes((0.9,0.10,0.02,0.85))
#         fc=fig.colorbar(pylabmpl.cm.ScalarMappable(norm=norm_, cmap=cmap_),
#                      cax=ax_, orientation='vertical')
#         #fc.set_ticks([0.5,1.5,3,4.5])
#         fc.set_ticks([1,2,3.5,5])
#         fc.set_ticklabels(['Water','Super-\ncooled \nWater','Ice','Mixed\nPhase'])
#         for tick in fc.ax.get_yticklabels(): 
#             tick.set_fontsize(15)
#             tick.set_weight('semibold')
#         #cb = plt.colorbar(ticks=range(0,5), label='Group')
#         #cb.ax.tick_params(length=0)
#         #np.save('./supercooled_water/cloud_mask_'+day+'.npy',cloud)
#         #print yuntime
#         np.save('./SWC/cloud_'+sday+'.npy',cloud)
#         fig.savefig('./Pics/cloud_'+str(day)+'_'+str(minute2average)+'min_mean_'+str(F_threshold)+'.png')
#         plt.show()
#         plt.close()
# #%%
#     except:
#         continue
#%%
#ims(nrb>0.3)
#ims(cloud)
# fig = plt.figure(figsize=(16,9))
# ax = fig.add_axes((0.07,0.10,0.8,0.85)) 
# ax.pcolormesh(time,z,(nrb>0.3).T,cmap=plt.cm.Greys,alpha=0.5)
# #ax.pcolormesh(time,z,cloud_mask.T,norm=norm_,cmap=cmap_)
# plt.ylim(0,14)
# ax.set_xlim(TimeStart,TimeEnd)
# ax.xaxis.set_major_locator(mdates.HourLocator(interval = 3))
# ax.xaxis.set_minor_locator(mdates.HourLocator(interval = 1))
# ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))