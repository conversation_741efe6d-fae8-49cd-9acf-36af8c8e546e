function advanced_clustering_analysis()
% 高级聚类分析：探索多种聚类策略来找到BackScatter~0.04的过冷水聚类
% 测试不同的聚类数量和识别策略

clc; clear; close all;

%% -------- 设置路径 --------
lidarDir = 'D:\lidar\supercooledwaterday-hourly';
backscatterDir = 'D:\lidar\backscatter';
era5Dir = 'D:\lidar\supercooledwaterday-hourly\era5';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\advanced_clustering';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 参数设置 --------
% 过冷水温度范围
temp_max = 0;    % 最高温度 0°C
temp_min = -40;  % 最低温度 -40°C

fprintf('高级聚类分析参数:\n');
fprintf('  过冷水温度范围: %.1f°C 到 %.1f°C\n', temp_min, temp_max);
fprintf('  目标: 探索多种聚类策略，寻找BackScatter~0.04的过冷水聚类\n');

%% -------- 查找数据文件 --------
depolFiles = dir(fullfile(lidarDir, 'Depol_*.csv'));
fprintf('\n找到 %d 个Depol文件\n', length(depolFiles));

if isempty(depolFiles)
    error('未找到Depol文件，请检查数据路径');
end

% 分析全部文件
test_files = length(depolFiles);
fprintf('将分析全部 %d 个文件\n', test_files);

%% -------- 收集数据 --------
all_depol_data = [];
all_backscatter_data = [];
all_labels = [];
file_info = [];

for fileIdx = 1:test_files
    try
        % 获取文件信息
        depolFile = depolFiles(fileIdx).name;
        fprintf('\n=== 分析文件 %d/%d: %s ===\n', fileIdx, test_files, depolFile);
        
        % 从文件名提取日期
        dateMatch = regexp(depolFile, 'Depol_(\d{8})', 'tokens');
        if isempty(dateMatch)
            fprintf('  警告: 无法从文件名解析日期，跳过\n');
            continue;
        end
        dateStr = dateMatch{1}{1};
        dateFormatted = [dateStr(1:4), '-', dateStr(5:6), '-', dateStr(7:8)];
        
        % 查找对应文件
        backscatterFile = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', dateStr));
        backscatterFiles = dir(backscatterFile);
        if isempty(backscatterFiles)
            fprintf('  警告: 找不到对应的BackScatter文件，跳过\n');
            continue;
        end
        
        era5File = fullfile(era5Dir, sprintf('era5_temp_geopotential_%s.nc', dateStr));
        if ~exist(era5File, 'file')
            fprintf('  警告: 找不到对应的ERA5文件，跳过\n');
            continue;
        end
        
        % 加载数据
        [lidar_data, success] = load_lidar_data_advanced(lidarDir, backscatterDir, depolFile, backscatterFiles(1).name);
        if ~success
            fprintf('  错误: 激光雷达数据加载失败\n');
            continue;
        end
        
        [era5_data, success] = load_era5_data_advanced(era5File);
        if ~success
            fprintf('  错误: ERA5数据加载失败\n');
            continue;
        end
        
        % 简化的云识别
        [cloud_mask, ~] = identify_clouds_advanced(lidar_data);
        fprintf('  云识别完成，云覆盖率: %.1f%%\n', sum(cloud_mask(:))/numel(cloud_mask)*100);
        
        % 温度约束
        [temp_mask, height_info] = apply_temperature_constraint_advanced(lidar_data, era5_data, temp_min, temp_max);
        fprintf('  温度约束完成，过冷区域覆盖率: %.1f%%\n', sum(temp_mask(:))/numel(temp_mask)*100);
        
        % 综合约束条件：云层 + 过冷温度
        base_mask = cloud_mask & temp_mask;
        fprintf('  综合约束后覆盖率: %.1f%%\n', sum(base_mask(:))/numel(base_mask)*100);
        
        % 提取过冷水区域的数据
        [depol_samples, backscatter_samples] = extract_supercooled_samples_advanced(lidar_data, base_mask);
        
        if ~isempty(depol_samples)
            all_depol_data = [all_depol_data; depol_samples];
            all_backscatter_data = [all_backscatter_data; backscatter_samples];
            all_labels = [all_labels; ones(length(depol_samples), 1) * fileIdx];
            
            file_info = [file_info; struct('date', dateFormatted, 'samples', length(depol_samples), ...
                                          'height_info', height_info)];
            
            fprintf('  提取样本数: %d\n', length(depol_samples));
        end
        
        fprintf('  处理完成: %s\n', dateFormatted);
        
    catch ME
        fprintf('  处理文件 %s 时出错: %s\n', depolFile, ME.message);
        continue;
    end
end

%% -------- 高级聚类分析 --------
if ~isempty(all_depol_data)
    fprintf('\n开始高级聚类分析...\n');
    fprintf('总样本数: %d\n', length(all_depol_data));
    
    % 执行多种聚类策略
    clustering_results = perform_advanced_clustering(all_depol_data, all_backscatter_data, all_labels);
    
    % 生成分析图表
    generate_advanced_clustering_plots(all_depol_data, all_backscatter_data, all_labels, clustering_results, file_info, outputDir);
    
    % 生成详细报告
    generate_advanced_threshold_report(clustering_results, file_info, outputDir);
    
    fprintf('\n高级聚类分析完成！\n');
    fprintf('结果保存在: %s\n', outputDir);
else
    fprintf('\n没有收集到有效的样本数据\n');
end

end

%% -------- 数据加载函数（复用之前的） --------
function [lidar_data, success] = load_lidar_data_advanced(lidarDir, backscatterDir, depolFile, backscatterFile)
    success = false;
    lidar_data = struct();
    
    try
        % 加载数据
        depolPath = fullfile(lidarDir, depolFile);
        raw_depol = readmatrix(depolPath);
        
        backscatterPath = fullfile(backscatterDir, backscatterFile);
        raw_backscatter = readmatrix(backscatterPath);
        
        % 处理高度数据
        height = raw_depol(:,1);
        valid_idx = ~isnan(height);
        height = height(valid_idx);
        
        % 限制高度范围到4km以内
        height_mask = height <= 4000;
        height = height(height_mask);
        
        % 获取数据（限制时间点数以提高处理速度）
        max_time_points = 480;  % 8小时的数据用于聚类分析
        depol_data = raw_depol(valid_idx, 2:min(end, max_time_points+1));
        depol_data = depol_data(height_mask, :);
        
        backscatter_data = raw_backscatter(valid_idx, 2:min(end, max_time_points+1));
        backscatter_data = backscatter_data(height_mask, :);
        
        % 创建时间向量
        num_times = size(depol_data, 2);
        time_vector = 0:(num_times-1);
        
        % 存储数据
        lidar_data.height = height;
        lidar_data.time = time_vector;
        lidar_data.depol = double(depol_data);
        lidar_data.backscatter = double(backscatter_data);
        
        fprintf('    激光雷达数据加载成功 (高度: %.0f-%.0f m, 时间: %d 分钟)\n', ...
                min(height), max(height), num_times);
        
        success = true;
        
    catch ME
        fprintf('    激光雷达数据加载失败: %s\n', ME.message);
    end
end

function [era5_data, success] = load_era5_data_advanced(era5File)
    success = false;
    era5_data = struct();
    
    try
        % 读取ERA5数据
        temperature = ncread(era5File, 't');
        geopotential = ncread(era5File, 'z');
        pressure_levels = ncread(era5File, 'pressure_level');
        time = ncread(era5File, 'valid_time');
        
        % 转换单位
        temperature_celsius = squeeze(temperature) - 273.15;
        geopotential_height = squeeze(geopotential) / 9.80665;
        
        % 存储数据
        era5_data.temperature = temperature_celsius;
        era5_data.height = geopotential_height;
        era5_data.pressure = pressure_levels;
        era5_data.time = time;
        
        fprintf('    ERA5数据加载成功\n');
        success = true;
        
    catch ME
        fprintf('    ERA5数据加载失败: %s\n', ME.message);
    end
end

function [cloud_mask, cloud_stats] = identify_clouds_advanced(lidar_data)
    % 简化版本，使用后向散射阈值识别云层
    fprintf('    应用简化云识别算法...\n');
    
    % 使用后向散射阈值来识别云层
    cloud_threshold = 1e-5;  % 后向散射阈值
    cloud_mask = lidar_data.backscatter > cloud_threshold;
    
    % 统计信息
    cloud_stats = struct();
    cloud_stats.threshold_detections = sum(cloud_mask(:));
    cloud_stats.water_cloud_detections = 0;
    cloud_stats.gradient_detections = 0;
end

function [temp_mask, height_info] = apply_temperature_constraint_advanced(lidar_data, era5_data, temp_min, temp_max)
    fprintf('    应用过冷水温度约束...\n');

    % 初始化温度掩码
    temp_mask = false(size(lidar_data.depol));

    % 计算平均温度剖面
    mean_temp_profile = mean(era5_data.temperature, 2);
    mean_height_profile = mean(era5_data.height, 2);

    % 插值到激光雷达高度网格
    temp_interp_mean = interp1(mean_height_profile, mean_temp_profile, lidar_data.height, 'linear', 'extrap');

    % 找到过冷水温度范围对应的高度范围
    supercooled_indices = find((temp_interp_mean >= temp_min) & (temp_interp_mean <= temp_max));

    if ~isempty(supercooled_indices)
        supercooled_height_min = lidar_data.height(min(supercooled_indices));
        supercooled_height_max = lidar_data.height(max(supercooled_indices));

        height_info = struct();
        height_info.supercooled_range = [supercooled_height_min, supercooled_height_max];

        fprintf('    过冷水高度范围: %.0f - %.0f m\n', supercooled_height_min, supercooled_height_max);
    else
        height_info = struct();
        height_info.supercooled_range = [NaN, NaN];

        fprintf('    警告: 未找到过冷水温度区域\n');
    end

    % 对每个时间点应用温度约束
    for t = 1:length(lidar_data.time)
        era5_time_idx = min(t, size(era5_data.temperature, 2));
        temp_profile = era5_data.temperature(:, era5_time_idx);
        height_profile = era5_data.height(:, era5_time_idx);
        temp_interp = interp1(height_profile, temp_profile, lidar_data.height, 'linear', 'extrap');
        temp_condition = (temp_interp >= temp_min) & (temp_interp <= temp_max);
        temp_mask(:, t) = temp_condition;
    end
end

function [depol_samples, backscatter_samples] = extract_supercooled_samples_advanced(lidar_data, base_mask)
    % 从过冷水区域提取Depol和BackScatter样本

    % 获取有效的样本点
    valid_indices = base_mask & isfinite(lidar_data.depol) & isfinite(lidar_data.backscatter) & ...
                    (lidar_data.depol > 0) & (lidar_data.backscatter > 0);

    % 提取样本
    depol_samples = lidar_data.depol(valid_indices);
    backscatter_samples = lidar_data.backscatter(valid_indices);

    % 数据预处理：移除极端值
    depol_p99 = prctile(depol_samples, 99);
    backscatter_p99 = prctile(backscatter_samples, 99);

    valid_samples = (depol_samples <= depol_p99) & (backscatter_samples <= backscatter_p99);
    depol_samples = depol_samples(valid_samples);
    backscatter_samples = backscatter_samples(valid_samples);

    % 随机采样以减少计算量（每个文件最多5000个样本）
    if length(depol_samples) > 5000
        sample_indices = randperm(length(depol_samples), 5000);
        depol_samples = depol_samples(sample_indices);
        backscatter_samples = backscatter_samples(sample_indices);
    end
end

%% -------- 高级聚类分析函数 --------
function clustering_results = perform_advanced_clustering(depol_data, backscatter_data, labels)
    fprintf('执行高级聚类分析...\n');

    % 数据预处理：对数变换和标准化
    log_depol = log10(depol_data + 1e-6);
    log_backscatter = log10(backscatter_data + 1e-6);

    % 标准化数据
    data_matrix = [log_depol, log_backscatter];
    data_normalized = (data_matrix - mean(data_matrix)) ./ std(data_matrix);

    % 测试不同的聚类数量 (k=2到k=6)
    fprintf('  测试不同聚类数量...\n');
    clustering_results = struct();
    clustering_results.original_data = [depol_data, backscatter_data];
    clustering_results.normalized_data = data_normalized;
    clustering_results.log_data = [log_depol, log_backscatter];
    clustering_results.labels = labels;

    % 存储不同k值的聚类结果
    k_values = 2:6;
    clustering_results.k_values = k_values;
    clustering_results.kmeans_results = cell(length(k_values), 1);
    clustering_results.cluster_analysis = cell(length(k_values), 1);

    for i = 1:length(k_values)
        k = k_values(i);
        fprintf('    执行K-means聚类 (k=%d)...\n', k);

        [kmeans_idx, kmeans_centers] = simple_kmeans_advanced(data_normalized, k);

        % 分析每个聚类的特征
        cluster_analysis = analyze_clusters_advanced(depol_data, backscatter_data, kmeans_idx, k);

        clustering_results.kmeans_results{i} = struct('idx', kmeans_idx, 'centers', kmeans_centers, 'k', k);
        clustering_results.cluster_analysis{i} = cluster_analysis;
    end

    % 寻找最接近目标BackScatter=0.04的聚类
    [best_strategy, best_cluster_info] = find_best_backscatter_cluster(clustering_results);
    clustering_results.best_strategy = best_strategy;
    clustering_results.best_cluster_info = best_cluster_info;

    fprintf('高级聚类分析完成\n');
end

%% -------- 分析聚类特征 --------
function cluster_analysis = analyze_clusters_advanced(depol_data, backscatter_data, kmeans_idx, k)
    cluster_analysis = struct();
    cluster_analysis.k = k;
    cluster_analysis.cluster_stats = cell(k, 1);

    for i = 1:k
        cluster_mask = kmeans_idx == i;
        cluster_depol = depol_data(cluster_mask);
        cluster_backscatter = backscatter_data(cluster_mask);

        if ~isempty(cluster_depol)
            stats = struct();
            stats.cluster_id = i;
            stats.size = length(cluster_depol);
            stats.percentage = length(cluster_depol) / length(depol_data) * 100;

            % Depol统计
            stats.depol_mean = mean(cluster_depol);
            stats.depol_std = std(cluster_depol);
            stats.depol_median = median(cluster_depol);
            stats.depol_p25 = prctile(cluster_depol, 25);
            stats.depol_p75 = prctile(cluster_depol, 75);

            % BackScatter统计
            stats.backscatter_mean = mean(cluster_backscatter);
            stats.backscatter_std = std(cluster_backscatter);
            stats.backscatter_median = median(cluster_backscatter);
            stats.backscatter_p25 = prctile(cluster_backscatter, 25);
            stats.backscatter_p75 = prctile(cluster_backscatter, 75);

            % 与目标值的距离
            stats.depol_distance_to_01 = abs(stats.depol_mean - 0.1);
            stats.backscatter_distance_to_004 = abs(stats.backscatter_mean - 0.04);

            % 综合评分（越小越好）
            stats.combined_score = stats.depol_distance_to_01/0.1 + stats.backscatter_distance_to_004/0.04;

            cluster_analysis.cluster_stats{i} = stats;

            fprintf('      聚类%d: 样本=%d(%.1f%%), Depol=%.3f, BackScatter=%.4f, 评分=%.2f\n', ...
                    i, stats.size, stats.percentage, stats.depol_mean, stats.backscatter_mean, stats.combined_score);
        end
    end
end

%% -------- 寻找最佳BackScatter聚类 --------
function [best_strategy, best_cluster_info] = find_best_backscatter_cluster(clustering_results)
    fprintf('  寻找最接近BackScatter=0.04的聚类...\n');

    best_strategy = struct();
    best_cluster_info = struct();

    min_backscatter_distance = inf;
    best_k = 2;
    best_cluster_id = 1;

    % 遍历所有k值和聚类
    for i = 1:length(clustering_results.k_values)
        k = clustering_results.k_values(i);
        cluster_analysis = clustering_results.cluster_analysis{i};

        for j = 1:k
            if ~isempty(cluster_analysis.cluster_stats{j})
                stats = cluster_analysis.cluster_stats{j};

                % 寻找BackScatter最接近0.04的聚类
                if stats.backscatter_distance_to_004 < min_backscatter_distance
                    min_backscatter_distance = stats.backscatter_distance_to_004;
                    best_k = k;
                    best_cluster_id = j;
                    best_cluster_info = stats;
                end
            end
        end
    end

    % 找到最佳聚类后，获取对应的聚类索引
    best_kmeans_idx = clustering_results.kmeans_results{best_k-1}.idx;  % k-1因为数组从1开始
    best_cluster_mask = best_kmeans_idx == best_cluster_id;

    best_strategy.k = best_k;
    best_strategy.cluster_id = best_cluster_id;
    best_strategy.cluster_mask = best_cluster_mask;
    best_strategy.backscatter_distance = min_backscatter_distance;

    fprintf('    最佳聚类: k=%d, 聚类%d\n', best_k, best_cluster_id);
    fprintf('    BackScatter均值: %.4f (距离目标0.04: %.4f)\n', ...
            best_cluster_info.backscatter_mean, min_backscatter_distance);
    fprintf('    Depol均值: %.3f (距离目标0.1: %.3f)\n', ...
            best_cluster_info.depol_mean, best_cluster_info.depol_distance_to_01);

    % 计算阈值建议
    depol_data = clustering_results.original_data(:, 1);
    backscatter_data = clustering_results.original_data(:, 2);

    cluster_depol = depol_data(best_cluster_mask);
    cluster_backscatter = backscatter_data(best_cluster_mask);

    % 基于最佳聚类计算阈值
    threshold_suggestions = struct();
    threshold_suggestions.depol_p75 = prctile(cluster_depol, 75);
    threshold_suggestions.depol_p90 = prctile(cluster_depol, 90);
    threshold_suggestions.depol_p95 = prctile(cluster_depol, 95);
    threshold_suggestions.backscatter_p25 = prctile(cluster_backscatter, 25);
    threshold_suggestions.backscatter_p10 = prctile(cluster_backscatter, 10);
    threshold_suggestions.backscatter_p5 = prctile(cluster_backscatter, 5);

    % 寻找最接近目标值的阈值
    [~, best_depol_idx] = min(abs([threshold_suggestions.depol_p75, threshold_suggestions.depol_p90, threshold_suggestions.depol_p95] - 0.1));
    [~, best_backscatter_idx] = min(abs([threshold_suggestions.backscatter_p25, threshold_suggestions.backscatter_p10, threshold_suggestions.backscatter_p5] - 0.04));

    depol_methods = {'P75', 'P90', 'P95'};
    backscatter_methods = {'P25', 'P10', 'P5'};
    depol_values = [threshold_suggestions.depol_p75, threshold_suggestions.depol_p90, threshold_suggestions.depol_p95];
    backscatter_values = [threshold_suggestions.backscatter_p25, threshold_suggestions.backscatter_p10, threshold_suggestions.backscatter_p5];

    best_strategy.recommended_depol = depol_values(best_depol_idx);
    best_strategy.recommended_backscatter = backscatter_values(best_backscatter_idx);
    best_strategy.depol_method = depol_methods{best_depol_idx};
    best_strategy.backscatter_method = backscatter_methods{best_backscatter_idx};
    best_strategy.threshold_suggestions = threshold_suggestions;

    fprintf('    推荐阈值: Depol<%.3f(%s), BackScatter>%.4f(%s)\n', ...
            best_strategy.recommended_depol, best_strategy.depol_method, ...
            best_strategy.recommended_backscatter, best_strategy.backscatter_method);
end

%% -------- 简化K-means实现 --------
function [idx, centers] = simple_kmeans_advanced(data, k)
    [n, d] = size(data);

    % 随机初始化聚类中心
    rng(42); % 设置随机种子以获得可重复结果
    centers = data(randperm(n, k), :);

    max_iter = 100;
    tolerance = 1e-6;

    for iter = 1:max_iter
        % 分配每个点到最近的聚类中心
        distances = zeros(n, k);
        for i = 1:k
            diff = data - repmat(centers(i, :), n, 1);
            distances(:, i) = sum(diff.^2, 2);
        end

        [~, idx] = min(distances, [], 2);

        % 更新聚类中心
        new_centers = zeros(k, d);
        for i = 1:k
            cluster_points = data(idx == i, :);
            if ~isempty(cluster_points)
                new_centers(i, :) = mean(cluster_points, 1);
            else
                new_centers(i, :) = centers(i, :); % 保持原中心
            end
        end

        % 检查收敛
        if max(max(abs(new_centers - centers))) < tolerance
            break;
        end

        centers = new_centers;
    end
end

%% -------- 生成高级聚类分析图表 --------
function generate_advanced_clustering_plots(depol_data, backscatter_data, labels, clustering_results, file_info, outputDir)
    fprintf('生成高级聚类分析图表...\n');

    % 图1: 不同k值的聚类结果对比
    fig1 = figure('Position', [100, 100, 1600, 1200]);

    k_values = clustering_results.k_values;
    num_k = length(k_values);

    for i = 1:num_k
        subplot(2, 3, i);
        k = k_values(i);
        kmeans_idx = clustering_results.kmeans_results{i}.idx;

        scatter(depol_data, backscatter_data, 20, kmeans_idx, 'filled');
        xlabel('Depol');
        ylabel('BackScatter');
        title(sprintf('K-means聚类 (k=%d)', k));
        grid on;
        set(gca, 'XScale', 'log', 'YScale', 'log');
        colorbar;
    end

    % 最后一个子图显示最佳聚类
    subplot(2, 3, 6);
    best_mask = clustering_results.best_strategy.cluster_mask;
    scatter(depol_data(~best_mask), backscatter_data(~best_mask), 20, [0.7 0.7 0.7], 'filled');
    hold on;
    scatter(depol_data(best_mask), backscatter_data(best_mask), 20, [0.2 0.6 0.9], 'filled');
    xlabel('Depol');
    ylabel('BackScatter');
    title(sprintf('最佳聚类 (k=%d, 聚类%d)', clustering_results.best_strategy.k, clustering_results.best_strategy.cluster_id));
    legend('其他', '目标聚类', 'Location', 'best');
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 添加目标线
    xline(0.1, 'r--', 'LineWidth', 2, 'Label', 'Depol=0.1');
    yline(0.04, 'r--', 'LineWidth', 2, 'Label', 'BackScatter=0.04');
    hold off;

    sgtitle('高级聚类分析：不同k值对比', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图1
    outputFile1 = fullfile(outputDir, 'advanced_clustering_k_comparison.png');
    print(fig1, outputFile1, '-dpng', '-r300');
    fprintf('保存k值对比图: %s\n', outputFile1);
    close(fig1);

    % 图2: 最佳聚类详细分析
    fig2 = figure('Position', [100, 100, 1600, 800]);

    best_mask = clustering_results.best_strategy.cluster_mask;
    target_depol = depol_data(best_mask);
    target_backscatter = backscatter_data(best_mask);
    other_depol = depol_data(~best_mask);
    other_backscatter = backscatter_data(~best_mask);

    % 子图1: 散点图
    subplot(1, 3, 1);
    scatter(other_depol, other_backscatter, 20, [0.7 0.7 0.7], 'filled');
    hold on;
    scatter(target_depol, target_backscatter, 20, [0.2 0.6 0.9], 'filled');
    xlabel('Depol');
    ylabel('BackScatter');
    title('最佳聚类识别结果');
    legend('其他聚类', '目标聚类', 'Location', 'best');
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 添加目标线和推荐阈值线
    xline(0.1, 'r--', 'LineWidth', 2, 'Label', '目标Depol=0.1');
    yline(0.04, 'r--', 'LineWidth', 2, 'Label', '目标BackScatter=0.04');
    xline(clustering_results.best_strategy.recommended_depol, 'g--', 'LineWidth', 2, ...
          'Label', sprintf('推荐Depol=%.3f', clustering_results.best_strategy.recommended_depol));
    yline(clustering_results.best_strategy.recommended_backscatter, 'g--', 'LineWidth', 2, ...
          'Label', sprintf('推荐BackScatter=%.4f', clustering_results.best_strategy.recommended_backscatter));
    hold off;

    % 子图2: Depol分布对比
    subplot(1, 3, 2);
    histogram(target_depol, 50, 'FaceColor', [0.2 0.6 0.9], 'EdgeColor', 'none', 'Normalization', 'probability');
    hold on;
    histogram(other_depol, 50, 'FaceColor', [0.7 0.7 0.7], 'EdgeColor', 'none', 'Normalization', 'probability');
    xlabel('Depol');
    ylabel('概率密度');
    title('Depol分布对比');
    legend('目标聚类', '其他聚类', 'Location', 'best');
    set(gca, 'XScale', 'log');
    grid on;

    % 添加阈值线
    xline(0.1, 'r--', 'LineWidth', 2, 'Label', '目标0.1');
    xline(clustering_results.best_strategy.recommended_depol, 'g--', 'LineWidth', 2, ...
          'Label', sprintf('推荐%.3f', clustering_results.best_strategy.recommended_depol));
    hold off;

    % 子图3: BackScatter分布对比
    subplot(1, 3, 3);
    histogram(target_backscatter, 50, 'FaceColor', [0.2 0.6 0.9], 'EdgeColor', 'none', 'Normalization', 'probability');
    hold on;
    histogram(other_backscatter, 50, 'FaceColor', [0.7 0.7 0.7], 'EdgeColor', 'none', 'Normalization', 'probability');
    xlabel('BackScatter');
    ylabel('概率密度');
    title('BackScatter分布对比');
    legend('目标聚类', '其他聚类', 'Location', 'best');
    set(gca, 'XScale', 'log');
    grid on;

    % 添加阈值线
    xline(0.04, 'r--', 'LineWidth', 2, 'Label', '目标0.04');
    xline(clustering_results.best_strategy.recommended_backscatter, 'g--', 'LineWidth', 2, ...
          'Label', sprintf('推荐%.4f', clustering_results.best_strategy.recommended_backscatter));
    hold off;

    sgtitle(sprintf('最佳聚类分析 (k=%d, 聚类%d)', clustering_results.best_strategy.k, clustering_results.best_strategy.cluster_id), ...
            'FontSize', 16, 'FontWeight', 'bold');

    % 保存图2
    outputFile2 = fullfile(outputDir, 'best_cluster_analysis.png');
    print(fig2, outputFile2, '-dpng', '-r300');
    fprintf('保存最佳聚类分析图: %s\n', outputFile2);
    close(fig2);

    % 图3: 聚类评分对比
    generate_cluster_scoring_plot(clustering_results, outputDir);
end

%% -------- 生成聚类评分对比图 --------
function generate_cluster_scoring_plot(clustering_results, outputDir)
    fig = figure('Position', [100, 100, 1200, 800]);

    % 收集所有聚类的评分数据
    all_scores = [];
    all_k_values = [];
    all_cluster_ids = [];
    all_depol_means = [];
    all_backscatter_means = [];

    for i = 1:length(clustering_results.k_values)
        k = clustering_results.k_values(i);
        cluster_analysis = clustering_results.cluster_analysis{i};

        for j = 1:k
            if ~isempty(cluster_analysis.cluster_stats{j})
                stats = cluster_analysis.cluster_stats{j};
                all_scores = [all_scores; stats.combined_score];
                all_k_values = [all_k_values; k];
                all_cluster_ids = [all_cluster_ids; j];
                all_depol_means = [all_depol_means; stats.depol_mean];
                all_backscatter_means = [all_backscatter_means; stats.backscatter_mean];
            end
        end
    end

    % 子图1: 评分散点图
    subplot(2, 2, 1);
    scatter(all_depol_means, all_backscatter_means, 100, all_scores, 'filled');
    xlabel('Depol均值');
    ylabel('BackScatter均值');
    title('聚类评分分布');
    colorbar;
    colormap(gca, 'jet');
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 标记最佳聚类
    hold on;
    best_depol = clustering_results.best_cluster_info.depol_mean;
    best_backscatter = clustering_results.best_cluster_info.backscatter_mean;
    scatter(best_depol, best_backscatter, 200, 'r', 'x', 'LineWidth', 3);

    % 添加目标线
    xline(0.1, 'r--', 'LineWidth', 2);
    yline(0.04, 'r--', 'LineWidth', 2);
    hold off;

    % 子图2: 不同k值的最佳评分
    subplot(2, 2, 2);
    k_best_scores = zeros(size(clustering_results.k_values));
    for i = 1:length(clustering_results.k_values)
        k = clustering_results.k_values(i);
        k_mask = all_k_values == k;
        k_best_scores(i) = min(all_scores(k_mask));
    end

    bar(clustering_results.k_values, k_best_scores, 'FaceColor', [0.3 0.6 0.9]);
    xlabel('聚类数量 k');
    ylabel('最佳评分');
    title('不同k值的最佳聚类评分');
    grid on;

    % 标记最佳k值
    [~, best_k_idx] = min(k_best_scores);
    hold on;
    bar(clustering_results.k_values(best_k_idx), k_best_scores(best_k_idx), 'FaceColor', [0.9 0.3 0.3]);
    hold off;

    % 子图3: 阈值建议对比
    subplot(2, 2, 3);
    ts = clustering_results.best_strategy.threshold_suggestions;
    depol_methods = {'P75', 'P90', 'P95'};
    depol_values = [ts.depol_p75, ts.depol_p90, ts.depol_p95];

    bar(depol_values, 'FaceColor', [0.3 0.6 0.9]);
    set(gca, 'XTickLabel', depol_methods);
    ylabel('Depol阈值');
    title('Depol阈值建议');
    grid on;

    % 添加目标线
    hold on;
    yline(0.1, 'r--', 'LineWidth', 2, 'Label', '目标0.1');
    hold off;

    % 子图4: BackScatter阈值建议对比
    subplot(2, 2, 4);
    backscatter_methods = {'P25', 'P10', 'P5'};
    backscatter_values = [ts.backscatter_p25, ts.backscatter_p10, ts.backscatter_p5];

    bar(backscatter_values, 'FaceColor', [0.9 0.6 0.3]);
    set(gca, 'XTickLabel', backscatter_methods);
    ylabel('BackScatter阈值');
    title('BackScatter阈值建议');
    grid on;

    % 添加目标线
    hold on;
    yline(0.04, 'r--', 'LineWidth', 2, 'Label', '目标0.04');
    hold off;

    sgtitle('聚类评分和阈值建议分析', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, 'cluster_scoring_analysis.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存聚类评分分析图: %s\n', outputFile);

    close(fig);
end

%% -------- 生成高级阈值报告 --------
function generate_advanced_threshold_report(clustering_results, file_info, outputDir)
    fprintf('生成高级阈值建议报告...\n');

    % 创建报告文件
    reportFile = fullfile(outputDir, 'advanced_clustering_threshold_report.txt');

    fid = fopen(reportFile, 'w');
    if fid == -1
        fprintf('错误: 无法创建报告文件\n');
        return;
    end

    try
        % 写入报告头部
        fprintf(fid, '========================================\n');
        fprintf(fid, '高级聚类分析阈值建议报告\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '生成时间: %s\n', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
        fprintf(fid, '分析目的: 探索多种聚类策略，寻找BackScatter~0.04的过冷水聚类\n\n');

        % 数据基本信息
        fprintf(fid, '1. 数据基本信息\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '分析文件数: %d\n', length(file_info));
        fprintf(fid, '总样本数: %d\n', size(clustering_results.original_data, 1));

        for i = 1:length(file_info)
            fprintf(fid, '  %s: %d 样本\n', file_info(i).date, file_info(i).samples);
        end
        fprintf(fid, '\n');

        % 聚类策略分析
        fprintf(fid, '2. 聚类策略分析\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '测试的聚类数量: k = %d 到 %d\n', min(clustering_results.k_values), max(clustering_results.k_values));
        fprintf(fid, '评分标准: |Depol_mean - 0.1|/0.1 + |BackScatter_mean - 0.04|/0.04\n\n');

        % 各k值的最佳聚类
        fprintf(fid, '各k值的最佳聚类:\n');
        for i = 1:length(clustering_results.k_values)
            k = clustering_results.k_values(i);
            cluster_analysis = clustering_results.cluster_analysis{i};

            % 找到该k值下的最佳聚类
            best_score = inf;
            best_cluster = 1;
            for j = 1:k
                if ~isempty(cluster_analysis.cluster_stats{j})
                    stats = cluster_analysis.cluster_stats{j};
                    if stats.combined_score < best_score
                        best_score = stats.combined_score;
                        best_cluster = j;
                        best_stats = stats;
                    end
                end
            end

            fprintf(fid, '  k=%d: 聚类%d, 评分=%.3f, Depol=%.4f, BackScatter=%.6f\n', ...
                    k, best_cluster, best_score, best_stats.depol_mean, best_stats.backscatter_mean);
        end
        fprintf(fid, '\n');

        % 最佳聚类详细信息
        fprintf(fid, '3. 最佳聚类详细信息\n');
        fprintf(fid, '----------------------------------------\n');
        best_info = clustering_results.best_cluster_info;
        fprintf(fid, '最佳聚类: k=%d, 聚类%d\n', clustering_results.best_strategy.k, clustering_results.best_strategy.cluster_id);
        fprintf(fid, '样本数: %d (%.1f%%)\n', best_info.size, best_info.percentage);
        fprintf(fid, '\n');

        fprintf(fid, 'Depol统计:\n');
        fprintf(fid, '  均值: %.4f\n', best_info.depol_mean);
        fprintf(fid, '  标准差: %.4f\n', best_info.depol_std);
        fprintf(fid, '  中位数: %.4f\n', best_info.depol_median);
        fprintf(fid, '  25分位数: %.4f\n', best_info.depol_p25);
        fprintf(fid, '  75分位数: %.4f\n', best_info.depol_p75);
        fprintf(fid, '  与目标0.1的距离: %.4f\n', best_info.depol_distance_to_01);
        fprintf(fid, '\n');

        fprintf(fid, 'BackScatter统计:\n');
        fprintf(fid, '  均值: %.6f\n', best_info.backscatter_mean);
        fprintf(fid, '  标准差: %.6f\n', best_info.backscatter_std);
        fprintf(fid, '  中位数: %.6f\n', best_info.backscatter_median);
        fprintf(fid, '  25分位数: %.6f\n', best_info.backscatter_p25);
        fprintf(fid, '  75分位数: %.6f\n', best_info.backscatter_p75);
        fprintf(fid, '  与目标0.04的距离: %.6f\n', best_info.backscatter_distance_to_004);
        fprintf(fid, '\n');

        % 阈值建议
        fprintf(fid, '4. 基于最佳聚类的阈值建议\n');
        fprintf(fid, '----------------------------------------\n');
        ts = clustering_results.best_strategy.threshold_suggestions;

        fprintf(fid, 'Depol阈值建议:\n');
        fprintf(fid, '  75分位数: %.4f\n', ts.depol_p75);
        fprintf(fid, '  90分位数: %.4f\n', ts.depol_p90);
        fprintf(fid, '  95分位数: %.4f\n', ts.depol_p95);
        fprintf(fid, '\n');

        fprintf(fid, 'BackScatter阈值建议:\n');
        fprintf(fid, '  25分位数: %.6f\n', ts.backscatter_p25);
        fprintf(fid, '  10分位数: %.6f\n', ts.backscatter_p10);
        fprintf(fid, '  5分位数: %.6f\n', ts.backscatter_p5);
        fprintf(fid, '\n');

        % 推荐阈值组合
        fprintf(fid, '5. 推荐阈值组合\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '推荐Depol阈值: %.4f (%s方法)\n', ...
                clustering_results.best_strategy.recommended_depol, clustering_results.best_strategy.depol_method);
        fprintf(fid, '推荐BackScatter阈值: %.6f (%s方法)\n', ...
                clustering_results.best_strategy.recommended_backscatter, clustering_results.best_strategy.backscatter_method);
        fprintf(fid, '\n');

        fprintf(fid, '推荐参数组合:\n');
        fprintf(fid, '  Depol < %.4f\n', clustering_results.best_strategy.recommended_depol);
        fprintf(fid, '  BackScatter > %.6f\n', clustering_results.best_strategy.recommended_backscatter);
        fprintf(fid, '\n');

        % 与目标值对比
        fprintf(fid, '6. 与目标值对比分析\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '目标值: Depol < 0.1, BackScatter > 0.04\n');
        fprintf(fid, '聚类建议: Depol < %.4f, BackScatter > %.6f\n', ...
                clustering_results.best_strategy.recommended_depol, clustering_results.best_strategy.recommended_backscatter);

        depol_diff = clustering_results.best_strategy.recommended_depol - 0.1;
        backscatter_diff = clustering_results.best_strategy.recommended_backscatter - 0.04;

        fprintf(fid, 'Depol差异: %.4f (%.1f%%)\n', depol_diff, depol_diff/0.1*100);
        fprintf(fid, 'BackScatter差异: %.6f (%.1f%%)\n', backscatter_diff, backscatter_diff/0.04*100);
        fprintf(fid, '\n');

        % 结论和建议
        fprintf(fid, '7. 结论和建议\n');
        fprintf(fid, '----------------------------------------\n');

        if abs(best_info.backscatter_distance_to_004) < 0.01
            fprintf(fid, '成功找到BackScatter接近0.04的聚类！\n');
            fprintf(fid, '该聚类的BackScatter均值为%.6f，与目标值0.04非常接近。\n', best_info.backscatter_mean);
        elseif abs(best_info.backscatter_distance_to_004) < 0.02
            fprintf(fid, '找到BackScatter较接近0.04的聚类。\n');
            fprintf(fid, '该聚类的BackScatter均值为%.6f，与目标值0.04有一定差距。\n', best_info.backscatter_mean);
        else
            fprintf(fid, '未能找到BackScatter接近0.04的聚类。\n');
            fprintf(fid, '最接近的聚类BackScatter均值为%.6f，与目标值0.04差距较大。\n', best_info.backscatter_mean);
            fprintf(fid, '建议重新考虑BackScatter阈值的设定或数据预处理方法。\n');
        end

        fprintf(fid, '\n');
        fprintf(fid, '基于聚类分析的发现:\n');
        fprintf(fid, '1. 过冷水数据确实存在明显的聚类结构\n');
        fprintf(fid, '2. 不同的聚类数量k会产生不同的聚类结果\n');
        fprintf(fid, '3. 最佳聚类能够较好地平衡Depol和BackScatter两个指标\n');
        fprintf(fid, '4. 聚类方法为阈值选择提供了数据驱动的依据\n');
        fprintf(fid, '\n');

        fprintf(fid, '========================================\n');
        fprintf(fid, '报告结束\n');
        fprintf(fid, '========================================\n');

        fclose(fid);
        fprintf('保存高级阈值建议报告: %s\n', reportFile);

    catch ME
        fclose(fid);
        fprintf('生成报告时出错: %s\n', ME.message);
    end
end
