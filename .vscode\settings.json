{"Lingma.aI Chat.mcpToolsInAgentMode": true, "Lingma.DisplayLanguage": "简体中文", "Lingma.PreferredLanguage for AI Chat": "简体中文", "Lingma.aI Rules.projectRule": false, "Lingma.cloudModelManualTriggerGenerateLength": "long", "Lingma.cloudModelAutoTriggerGenerateLength": "long", "python-envs.defaultEnvManager": "ms-python.python:conda", "python-envs.defaultPackageManager": "ms-python.python:conda", "python-envs.pythonProjects": []}