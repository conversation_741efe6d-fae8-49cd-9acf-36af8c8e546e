function test_backscatter_thresholds()
% 测试不同BackScatter阈值对过冷水识别的影响
% 重点测试 0.04 vs 0.05

clc; clear; close all;

%% -------- 设置路径 --------
lidarDir = 'D:\lidar\supercooledwaterday-hourly';
backscatterDir = 'D:\lidar\backscatter';
era5Dir = 'D:\lidar\supercooledwaterday-hourly\era5';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\backscatter_threshold_test';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 参数设置 --------
% 过冷水温度范围
temp_max = 0;    % 最高温度 0°C
temp_min = -40;  % 最低温度 -40°C

% 重点测试的BackScatter阈值
backscatter_test_values = [0.04, 0.05];

% 固定的Depol阈值（基于之前的测试结果）
depol_threshold = 0.3;

% CloudMask参数
cloudmask_params = struct();
cloudmask_params.F_threshold = 800;
cloudmask_params.F2_threshold = 16;
cloudmask_params.maxHeight = 15.0;
cloudmask_params.k = 6;

fprintf('BackScatter阈值测试参数:\n');
fprintf('  测试BackScatter阈值: [%.2f, %.2f]\n', backscatter_test_values);
fprintf('  固定Depol阈值: %.1f\n', depol_threshold);
fprintf('  过冷水温度范围: %.1f°C 到 %.1f°C\n', temp_min, temp_max);

%% -------- 查找数据文件 --------
depolFiles = dir(fullfile(lidarDir, 'Depol_*.csv'));
fprintf('\n找到 %d 个Depol文件\n', length(depolFiles));

if isempty(depolFiles)
    error('未找到Depol文件，请检查数据路径');
end

% 选择几个代表性的文件进行测试
test_files = min(5, length(depolFiles));  % 测试前5个文件
fprintf('将测试前 %d 个文件\n', test_files);

%% -------- 处理文件 --------
all_results = [];

for fileIdx = 1:test_files
    try
        % 获取文件信息
        depolFile = depolFiles(fileIdx).name;
        fprintf('\n=== 处理文件 %d/%d: %s ===\n', fileIdx, test_files, depolFile);
        
        % 从文件名提取日期
        dateMatch = regexp(depolFile, 'Depol_(\d{8})', 'tokens');
        if isempty(dateMatch)
            fprintf('  警告: 无法从文件名解析日期，跳过\n');
            continue;
        end
        dateStr = dateMatch{1}{1};
        dateFormatted = [dateStr(1:4), '-', dateStr(5:6), '-', dateStr(7:8)];
        
        % 查找对应文件
        backscatterFile = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', dateStr));
        backscatterFiles = dir(backscatterFile);
        if isempty(backscatterFiles)
            fprintf('  警告: 找不到对应的BackScatter文件，跳过\n');
            continue;
        end
        
        era5File = fullfile(era5Dir, sprintf('era5_temp_geopotential_%s.nc', dateStr));
        if ~exist(era5File, 'file')
            fprintf('  警告: 找不到对应的ERA5文件，跳过\n');
            continue;
        end
        
        % 加载数据
        [lidar_data, success] = load_lidar_data_bs_test(lidarDir, backscatterDir, depolFile, backscatterFiles(1).name);
        if ~success
            fprintf('  错误: 激光雷达数据加载失败\n');
            continue;
        end
        
        [era5_data, success] = load_era5_data_bs_test(era5File);
        if ~success
            fprintf('  错误: ERA5数据加载失败\n');
            continue;
        end
        
        % CloudMask云识别
        [cloud_mask, cloud_stats] = identify_clouds_bs_test(lidar_data, cloudmask_params);
        fprintf('  CloudMask云识别完成，云覆盖率: %.1f%%\n', sum(cloud_mask(:))/numel(cloud_mask)*100);
        
        % 温度约束
        [temp_mask, height_info] = apply_temperature_constraint_bs_test(lidar_data, era5_data, temp_min, temp_max);
        fprintf('  温度约束完成，过冷区域覆盖率: %.1f%%\n', sum(temp_mask(:))/numel(temp_mask)*100);
        
        % 综合约束条件
        base_mask = cloud_mask & temp_mask;
        fprintf('  综合约束后覆盖率: %.1f%%\n', sum(base_mask(:))/numel(base_mask)*100);
        
        % 测试不同BackScatter阈值
        backscatter_results = test_different_backscatter_thresholds(lidar_data, base_mask, depol_threshold, backscatter_test_values);
        
        % 保存结果
        file_result = struct();
        file_result.date = dateFormatted;
        file_result.height_info = height_info;
        file_result.cloud_coverage = sum(cloud_mask(:))/numel(cloud_mask)*100;
        file_result.temp_coverage = sum(temp_mask(:))/numel(temp_mask)*100;
        file_result.base_coverage = sum(base_mask(:))/numel(base_mask)*100;
        file_result.backscatter_results = backscatter_results;
        
        all_results = [all_results; file_result];
        
        % 生成对比图
        generate_backscatter_comparison_plot(lidar_data, era5_data, base_mask, height_info, backscatter_results, dateFormatted, outputDir, depol_threshold);
        
        fprintf('  处理完成: %s\n', dateFormatted);
        
    catch ME
        fprintf('  处理文件 %s 时出错: %s\n', depolFile, ME.message);
        continue;
    end
end

%% -------- 生成汇总分析 --------
if ~isempty(all_results)
    generate_backscatter_summary_analysis(all_results, backscatter_test_values, depol_threshold, outputDir);
    fprintf('\n所有文件处理完成！\n');
    fprintf('结果保存在: %s\n', outputDir);
else
    fprintf('\n没有成功处理的文件\n');
end

end

%% -------- 子函数：测试不同BackScatter阈值 --------
function backscatter_results = test_different_backscatter_thresholds(lidar_data, base_mask, depol_threshold, backscatter_values)
    fprintf('    测试不同BackScatter阈值的效果...\n');
    
    backscatter_results = struct();
    backscatter_results.thresholds = backscatter_values;
    backscatter_results.detection_counts = zeros(size(backscatter_values));
    backscatter_results.coverage_rates = zeros(size(backscatter_values));
    
    base_count = sum(base_mask(:));
    
    for i = 1:length(backscatter_values)
        backscatter_thresh = backscatter_values(i);
        
        % 应用阈值条件
        depol_condition = lidar_data.depol < depol_threshold;
        backscatter_condition = lidar_data.backscatter > backscatter_thresh;
        
        % 综合所有条件
        supercooled_mask = base_mask & depol_condition & backscatter_condition;
        
        % 计算识别像素数和覆盖率
        detection_count = sum(supercooled_mask(:));
        if base_count > 0
            coverage_rate = detection_count / base_count * 100;
        else
            coverage_rate = 0;
        end
        
        backscatter_results.detection_counts(i) = detection_count;
        backscatter_results.coverage_rates(i) = coverage_rate;
        
        fprintf('      BackScatter > %.2f: 检测 %d 像素, 覆盖率 %.1f%%\n', ...
                backscatter_thresh, detection_count, coverage_rate);
    end
    
    % 计算差异
    if length(backscatter_values) == 2
        diff_count = backscatter_results.detection_counts(1) - backscatter_results.detection_counts(2);
        diff_rate = backscatter_results.coverage_rates(1) - backscatter_results.coverage_rates(2);
        fprintf('      差异 (0.04 vs 0.05): +%d 像素, +%.1f%%\n', diff_count, diff_rate);
    end
end

%% -------- 子函数：加载激光雷达数据 --------
function [lidar_data, success] = load_lidar_data_bs_test(lidarDir, backscatterDir, depolFile, backscatterFile)
    success = false;
    lidar_data = struct();

    try
        % 加载数据
        depolPath = fullfile(lidarDir, depolFile);
        raw_depol = readmatrix(depolPath);

        backscatterPath = fullfile(backscatterDir, backscatterFile);
        raw_backscatter = readmatrix(backscatterPath);

        % 处理高度数据
        height = raw_depol(:,1);
        valid_idx = ~isnan(height);
        height = height(valid_idx);

        % 限制高度范围到4km以内
        height_mask = height <= 4000;
        height = height(height_mask);

        % 获取24小时数据
        max_time_points = 1440;
        depol_data = raw_depol(valid_idx, 2:min(end, max_time_points+1));
        depol_data = depol_data(height_mask, :);

        backscatter_data = raw_backscatter(valid_idx, 2:min(end, max_time_points+1));
        backscatter_data = backscatter_data(height_mask, :);

        % 创建时间向量
        num_times = size(depol_data, 2);
        time_vector = 0:(num_times-1);

        % 存储数据
        lidar_data.height = height;
        lidar_data.time = time_vector;
        lidar_data.depol = double(depol_data);
        lidar_data.backscatter = double(backscatter_data);

        fprintf('    激光雷达数据加载成功 (高度: %.0f-%.0f m, 时间: %d 分钟)\n', ...
                min(height), max(height), num_times);

        success = true;

    catch ME
        fprintf('    激光雷达数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：加载ERA5数据 --------
function [era5_data, success] = load_era5_data_bs_test(era5File)
    success = false;
    era5_data = struct();

    try
        % 读取ERA5数据
        temperature = ncread(era5File, 't');
        geopotential = ncread(era5File, 'z');
        pressure_levels = ncread(era5File, 'pressure_level');
        time = ncread(era5File, 'valid_time');

        % 转换单位
        temperature_celsius = squeeze(temperature) - 273.15;
        geopotential_height = squeeze(geopotential) / 9.80665;

        % 存储数据
        era5_data.temperature = temperature_celsius;
        era5_data.height = geopotential_height;
        era5_data.pressure = pressure_levels;
        era5_data.time = time;

        fprintf('    ERA5数据加载成功\n');
        success = true;

    catch ME
        fprintf('    ERA5数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：简化的CloudMask云识别 --------
function [cloud_mask, cloud_stats] = identify_clouds_bs_test(lidar_data, ~)
    % 简化版本，直接使用阈值方法
    fprintf('    应用简化CloudMask算法...\n');

    % 使用简单的后向散射阈值来识别云层
    cloud_threshold = 1e-5;  % 后向散射阈值
    cloud_mask = lidar_data.backscatter > cloud_threshold;

    % 统计信息
    cloud_stats = struct();
    cloud_stats.threshold_detections = sum(cloud_mask(:));
    cloud_stats.water_cloud_detections = 0;
    cloud_stats.gradient_detections = 0;
end

%% -------- 子函数：温度约束 --------
function [temp_mask, height_info] = apply_temperature_constraint_bs_test(lidar_data, era5_data, temp_min, temp_max)
    fprintf('    应用过冷水温度约束...\n');

    % 初始化温度掩码
    temp_mask = false(size(lidar_data.depol));

    % 计算平均温度剖面
    mean_temp_profile = mean(era5_data.temperature, 2);
    mean_height_profile = mean(era5_data.height, 2);

    % 插值到激光雷达高度网格
    temp_interp_mean = interp1(mean_height_profile, mean_temp_profile, lidar_data.height, 'linear', 'extrap');

    % 找到过冷水温度范围对应的高度范围
    supercooled_indices = find((temp_interp_mean >= temp_min) & (temp_interp_mean <= temp_max));

    if ~isempty(supercooled_indices)
        supercooled_height_min = lidar_data.height(min(supercooled_indices));
        supercooled_height_max = lidar_data.height(max(supercooled_indices));

        % 找到0°C和-40°C对应的具体高度
        [~, zero_idx] = min(abs(temp_interp_mean - 0));
        [~, minus40_idx] = min(abs(temp_interp_mean - (-40)));

        zero_height = lidar_data.height(zero_idx);
        minus40_height = lidar_data.height(minus40_idx);

        height_info = struct();
        height_info.supercooled_range = [supercooled_height_min, supercooled_height_max];
        height_info.zero_height = zero_height;
        height_info.minus40_height = minus40_height;

        fprintf('    过冷水高度范围: %.0f - %.0f m\n', supercooled_height_min, supercooled_height_max);
    else
        height_info = struct();
        height_info.supercooled_range = [NaN, NaN];
        height_info.zero_height = NaN;
        height_info.minus40_height = NaN;

        fprintf('    警告: 未找到过冷水温度区域\n');
    end

    % 对每个时间点应用温度约束
    for t = 1:length(lidar_data.time)
        era5_time_idx = min(t, size(era5_data.temperature, 2));
        temp_profile = era5_data.temperature(:, era5_time_idx);
        height_profile = era5_data.height(:, era5_time_idx);
        temp_interp = interp1(height_profile, temp_profile, lidar_data.height, 'linear', 'extrap');
        temp_condition = (temp_interp >= temp_min) & (temp_interp <= temp_max);
        temp_mask(:, t) = temp_condition;
    end
end

%% -------- 子函数：生成BackScatter对比图 --------
function generate_backscatter_comparison_plot(lidar_data, era5_data, base_mask, height_info, backscatter_results, dateStr, outputDir, depol_threshold)
    fprintf('    生成BackScatter阈值对比图...\n');

    % 创建图形
    fig = figure('Position', [100, 100, 1600, 1000]);

    % 时间轴（转换为小时）
    time_hours = lidar_data.time / 60;

    % 子图1: 原始BackScatter数据
    subplot(2, 3, 1);
    pcolor(time_hours, lidar_data.height/1000, log10(lidar_data.backscatter + 1e-6));
    shading flat;
    colorbar;
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('原始后向散射 (log10)');
    ylim([0, 4]);

    % 子图2: 基础掩码（云层+温度约束）
    subplot(2, 3, 2);
    pcolor(time_hours, lidar_data.height/1000, double(base_mask));
    shading flat;
    colormap(gca, [1 1 1; 0 0.8 1]);
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('基础掩码 (云层+过冷温度)');
    ylim([0, 4]);

    % 子图3-4: 不同BackScatter阈值的识别结果
    backscatter_values = backscatter_results.thresholds;

    for i = 1:length(backscatter_values)
        subplot(2, 3, i+2);

        % 应用当前BackScatter阈值
        depol_condition = lidar_data.depol < depol_threshold;
        backscatter_condition = lidar_data.backscatter > backscatter_values(i);
        supercooled_mask = base_mask & depol_condition & backscatter_condition;

        pcolor(time_hours, lidar_data.height/1000, double(supercooled_mask));
        shading flat;
        colormap(gca, [1 1 1; 0 0.8 0.8]);
        xlabel('时间 (小时)');
        ylabel('高度 (km)');
        title(sprintf('BackScatter > %.2f\n覆盖率: %.1f%%', ...
                      backscatter_values(i), backscatter_results.coverage_rates(i)));
        ylim([0, 4]);
    end

    % 子图5: 统计对比
    subplot(2, 3, 5);
    bar(backscatter_values, backscatter_results.coverage_rates, 'FaceColor', [0.3 0.6 0.9]);
    xlabel('BackScatter阈值');
    ylabel('覆盖率 (%)');
    title('不同BackScatter阈值的覆盖率对比');
    grid on;

    % 添加数值标签
    for i = 1:length(backscatter_values)
        text(backscatter_values(i), backscatter_results.coverage_rates(i) + max(backscatter_results.coverage_rates)*0.02, ...
             sprintf('%.1f%%', backscatter_results.coverage_rates(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 计算并显示差异
    if length(backscatter_values) == 2
        diff_rate = backscatter_results.coverage_rates(1) - backscatter_results.coverage_rates(2);
        text(mean(backscatter_values), max(backscatter_results.coverage_rates)*0.8, ...
             sprintf('差异: +%.1f%%', diff_rate), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'red');
    end

    % 子图6: 详细统计信息
    subplot(2, 3, 6);
    axis off;

    % 创建统计文本
    stats_text = {
        sprintf('日期: %s', dateStr);
        '';
        '测试参数:';
        sprintf('  固定Depol阈值: %.1f', depol_threshold);
        sprintf('  BackScatter阈值: [%.2f, %.2f]', backscatter_values);
        '';
        '识别结果:';
    };

    for i = 1:length(backscatter_values)
        stats_text{end+1} = sprintf('  BS>%.2f: %d像素 (%.1f%%)', ...
                                   backscatter_values(i), ...
                                   backscatter_results.detection_counts(i), ...
                                   backscatter_results.coverage_rates(i));
    end

    if length(backscatter_values) == 2
        diff_count = backscatter_results.detection_counts(1) - backscatter_results.detection_counts(2);
        diff_rate = backscatter_results.coverage_rates(1) - backscatter_results.coverage_rates(2);
        stats_text = [stats_text; {
            '';
            '差异分析:';
            sprintf('  0.04 vs 0.05: +%d像素', diff_count);
            sprintf('  覆盖率提升: +%.1f%%', diff_rate);
        }];
    end

    if ~isnan(height_info.supercooled_range(1))
        stats_text = [stats_text; {
            '';
            '过冷水高度范围:';
            sprintf('  %.0f - %.0f m', height_info.supercooled_range(1), height_info.supercooled_range(2));
        }];
    end

    text(0.1, 0.9, stats_text, 'FontSize', 9, 'VerticalAlignment', 'top', ...
         'HorizontalAlignment', 'left', 'Units', 'normalized');

    % 添加总标题
    if ~isnan(height_info.supercooled_range(1))
        title_str = sprintf('BackScatter阈值测试 - %s (过冷水高度: %.0f-%.0f m)', ...
                           dateStr, height_info.supercooled_range(1), height_info.supercooled_range(2));
    else
        title_str = sprintf('BackScatter阈值测试 - %s', dateStr);
    end
    sgtitle(title_str, 'FontSize', 14, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, sprintf('backscatter_threshold_test_%s.png', strrep(dateStr, '-', '')));
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('    保存图像: %s\n', outputFile);

    close(fig);
end

%% -------- 子函数：生成汇总分析 --------
function generate_backscatter_summary_analysis(all_results, backscatter_values, depol_threshold, outputDir)
    fprintf('\n生成BackScatter阈值汇总分析...\n');

    % 创建汇总图
    fig = figure('Position', [100, 100, 1400, 800]);

    % 提取数据
    dates = {all_results.date};
    num_files = length(all_results);

    % 子图1: 不同日期的覆盖率对比
    subplot(2, 2, 1);
    coverage_matrix = zeros(num_files, length(backscatter_values));
    for i = 1:num_files
        coverage_matrix(i, :) = all_results(i).backscatter_results.coverage_rates;
    end

    bar(coverage_matrix);
    xlabel('日期');
    ylabel('覆盖率 (%)');
    title('不同日期的BackScatter阈值效果对比');
    legend(arrayfun(@(x) sprintf('BS>%.2f', x), backscatter_values, 'UniformOutput', false), 'Location', 'best');
    set(gca, 'XTickLabel', dates);
    xtickangle(45);
    grid on;

    % 子图2: 平均覆盖率对比
    subplot(2, 2, 2);
    mean_coverage = mean(coverage_matrix, 1);
    std_coverage = std(coverage_matrix, 1);

    bar(backscatter_values, mean_coverage, 'FaceColor', [0.3 0.6 0.9]);
    hold on;
    errorbar(backscatter_values, mean_coverage, std_coverage, 'k.', 'LineWidth', 2);
    xlabel('BackScatter阈值');
    ylabel('平均覆盖率 (%)');
    title('不同BackScatter阈值的平均效果');
    grid on;

    % 添加数值标签
    for i = 1:length(backscatter_values)
        text(backscatter_values(i), mean_coverage(i) + std_coverage(i) + max(mean_coverage)*0.05, ...
             sprintf('%.1f±%.1f%%', mean_coverage(i), std_coverage(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 计算并显示差异
    if length(backscatter_values) == 2
        diff_mean = mean_coverage(1) - mean_coverage(2);
        text(mean(backscatter_values), max(mean_coverage)*0.8, ...
             sprintf('平均差异: +%.1f%%', diff_mean), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'red');
    end

    % 子图3: 差异分析
    subplot(2, 2, 3);
    if length(backscatter_values) == 2
        differences = coverage_matrix(:, 1) - coverage_matrix(:, 2);
        bar(1:num_files, differences, 'FaceColor', [0.9 0.6 0.3]);
        xlabel('日期');
        ylabel('覆盖率差异 (%)');
        title('0.04 vs 0.05 覆盖率差异');
        set(gca, 'XTickLabel', dates);
        xtickangle(45);
        grid on;

        % 添加平均线
        hold on;
        plot([0.5, num_files+0.5], [diff_mean, diff_mean], 'r--', 'LineWidth', 2);
        text(num_files/2, diff_mean + max(differences)*0.1, ...
             sprintf('平均: +%.1f%%', diff_mean), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'Color', 'red');
    else
        text(0.5, 0.5, '需要两个阈值进行差异分析', 'HorizontalAlignment', 'center');
        title('差异分析');
    end

    % 子图4: 统计信息
    subplot(2, 2, 4);
    axis off;

    % 创建统计文本
    stats_text = {
        '汇总统计:';
        '';
        sprintf('测试文件数: %d', num_files);
        sprintf('固定Depol阈值: %.1f', depol_threshold);
        sprintf('测试BackScatter阈值: [%.2f, %.2f]', backscatter_values);
        '';
        '平均覆盖率:';
    };

    for i = 1:length(backscatter_values)
        stats_text{end+1} = sprintf('  BS > %.2f: %.1f%% (±%.1f%%)', ...
                                   backscatter_values(i), mean_coverage(i), std_coverage(i));
    end

    if length(backscatter_values) == 2
        stats_text = [stats_text; {
            '';
            '差异分析:';
            sprintf('  平均差异: +%.1f%%', diff_mean);
            sprintf('  最大差异: +%.1f%%', max(differences));
            sprintf('  最小差异: +%.1f%%', min(differences));
            '';
            '建议:';
        }];

        if diff_mean > 1
            stats_text{end+1} = '  推荐使用0.04阈值';
            stats_text{end+1} = '  (显著提高覆盖率)';
        elseif diff_mean > 0.5
            stats_text{end+1} = '  可考虑使用0.04阈值';
            stats_text{end+1} = '  (适度提高覆盖率)';
        else
            stats_text{end+1} = '  两个阈值效果相近';
            stats_text{end+1} = '  (可使用0.05阈值)';
        end
    end

    text(0.1, 0.9, stats_text, 'FontSize', 10, 'VerticalAlignment', 'top', ...
         'HorizontalAlignment', 'left', 'Units', 'normalized');

    % 添加总标题
    sgtitle('BackScatter阈值测试汇总分析 (0.04 vs 0.05)', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, 'backscatter_threshold_summary.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存汇总图像: %s\n', outputFile);

    % 保存数据
    save(fullfile(outputDir, 'backscatter_threshold_results.mat'), 'all_results', 'backscatter_values', 'mean_coverage', 'std_coverage', 'depol_threshold');
    fprintf('保存汇总数据: %s\n', fullfile(outputDir, 'backscatter_threshold_results.mat'));

    close(fig);

    % 输出最终建议
    if length(backscatter_values) == 2
        diff_mean = mean_coverage(1) - mean_coverage(2);
        fprintf('\n=== 最终建议 ===\n');
        fprintf('BackScatter阈值对比 (0.04 vs 0.05):\n');
        fprintf('  0.04阈值平均覆盖率: %.1f%% (±%.1f%%)\n', mean_coverage(1), std_coverage(1));
        fprintf('  0.05阈值平均覆盖率: %.1f%% (±%.1f%%)\n', mean_coverage(2), std_coverage(2));
        fprintf('  平均差异: +%.1f%%\n', diff_mean);

        if diff_mean > 1
            fprintf('  推荐: 使用0.04阈值 (显著提高覆盖率)\n');
        elseif diff_mean > 0.5
            fprintf('  推荐: 可考虑使用0.04阈值 (适度提高覆盖率)\n');
        else
            fprintf('  推荐: 两个阈值效果相近，可继续使用0.05阈值\n');
        end
    end
end
