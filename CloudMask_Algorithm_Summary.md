# CloudMask云识别算法详解

## 📋 算法概述

CloudMask是一种基于激光雷达后向散射数据的云层自动识别算法，通过分析信号的空间分布特征来识别大气中的云层区域。

## 🔄 算法流程

### 1. 数据预处理
```
输入: Backscatter(高度, 时间)
距离校正: P = Backscatter / height²
目的: 消除距离对信号强度的影响
```

### 2. 噪声估算
```
选择高度 > 15km 的数据作为噪声参考
计算标准差: sd = std(P[height > 15km])
噪声阈值: noise = k × sd (k=6)
```

### 3. 信号平滑
```
移动平均: Ps = movmean(P, 3)
目的: 减少随机噪声，保持主要特征
```

### 4. 双向扫描处理
**前向扫描 (PD1):**
```matlab
for zi = 1:(Nz-1)
    if abs(PD1(zi+1) - PD1(zi)) < noise
        PD1(zi+1) = PD1(zi);  % 保持连续性
    end
end
```

**后向扫描 (PD2):**
```matlab
for zi = 1:(Nz-1)
    if abs(PD2(Nz-zi) - PD2(Nz-zi+1)) < noise
        PD2(Nz-zi) = PD2(Nz-zi+1);  % 保持连续性
    end
end
```

**平均处理:**
```
PD = (PD1 + PD2) / 2
```

### 5. 层检测算法

#### 5.1 信号排序和基线计算
```matlab
[Rs, Is] = sort(PD);           % 按信号强度排序
MA = max(Rs); MI = min(Rs);    % 最大最小值
PE = (1:Nz) / Nz;              % 累积概率
y = PE × (MA - MI) + MI;       % 理论分布
PN(Is) = y;                    % 重新排列
B = ((Nz:-1:1) / Nz) × (MA - MI) + MI;  % 基线
```

#### 5.2 层边界检测
```matlab
% 寻找层的起始点: PN(zi+1) > B(zi+1) 且 PN(zi) <= B(zi)
% 计算层的面积: Area = sum(PN - B) for layer region
% 记录层的底部(base)和顶部(top)高度
```

### 6. 云层判断条件

#### 6.1 主要判断条件 (F/F2阈值)
```matlab
F = Area × PMX / (height_diff) / MA;
F2 = max(F1[base:top]);  % F1 = 100×(PN-B)/MA

if F > 800 || F2 > 16
    % 识别为云层
end
```

#### 6.2 水云检测条件
```matlab
if height < 2000 && median(PM) > 0.1 && prctile(PM,90) > 1
    if F > 1
        % 识别为水云
    end
end
```

#### 6.3 梯度检测条件
```matlab
% 计算对数NRB梯度
G = d(ln(PN × height²))/dz;

if max(G) > 3 || min(G) < -7
    % 识别为云层
end
```

## ⚙️ 关键参数

| 参数 | 值 | 说明 |
|------|----|----|
| F_threshold | 800 | 主要云层判断阈值 |
| F2_threshold | 16 | 辅助云层判断阈值 |
| k | 6 | 噪声系数 |
| maxHeight | 15.0 km | 最大处理高度 |
| water_cloud_threshold | 0.1 | 水云后向散射阈值 |
| gradient_high | 3 | 梯度上限 |
| gradient_low | -7 | 梯度下限 |

## 🎯 算法特点

### 多重判断机制
- **F/F2阈值**: 识别一般云层
- **水云条件**: 专门识别低层水云  
- **梯度条件**: 识别边界清晰的云层

### 自适应噪声处理
- 基于实际数据计算噪声水平
- 避免固定阈值的局限性

### 双向扫描技术
- 前向和后向扫描结合
- 增强信号连续性和稳定性

### 层检测方法
- 基于信号分布统计特征
- 能够识别复杂的多层云结构

## 📊 实际应用效果

在过冷水识别项目中的应用统计：

| 指标 | 数值 |
|------|------|
| 云覆盖率识别范围 | 5.2% - 14.9% |
| 主要检测方式 | F/F2阈值检测 |
| 水云检测次数 | 0次 (主要是高层云) |
| 梯度检测次数 | 0次 (信号边界不够陡峭) |

## 🔍 算法核心思想

CloudMask算法的核心思想是：

1. **信号连续性假设**: 在没有云层的区域，激光雷达信号应该是连续变化的
2. **统计分布分析**: 通过比较实际信号分布与理论基线的差异来识别异常区域
3. **多重验证机制**: 使用不同的物理条件来验证云层的存在

## 💡 算法优势

- ✅ **自动化程度高**: 无需人工干预
- ✅ **适应性强**: 能处理不同类型的云层
- ✅ **鲁棒性好**: 对噪声有较强的抗干扰能力
- ✅ **物理意义明确**: 基于激光雷达物理原理设计

## ⚠️ 使用注意事项

- 需要高质量的激光雷达数据
- 参数可能需要根据具体设备调整
- 对强降水情况的识别可能受限
- 适用于晴空和一般云层条件

---

*该算法在过冷水识别系统中作为云层预处理步骤，为后续的过冷水分析提供可靠的云层基础。*
