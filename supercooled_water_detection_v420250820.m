function supercooled_water_detection_v4()
% 过冷水识别程序 V4 - 正确实现CloudMask算法
% 基于激光雷达数据、正确的CloudMask云识别算法和ERA5温度数据
% 
% 重要说明：CloudMask算法的作用是识别并移除云层，保留降水等其他信号
% 因此我们需要使用CloudMask识别的云层区域作为我们的分析目标
%
% 使用方法：
% supercooled_water_detection_v4()

clc; clear; close all;

%% -------- 设置路径 --------
lidarDir = 'D:\lidar\supercooledwaterday-hourly';
backscatterDir = 'D:\lidar\backscatter';
era5Dir = 'D:\lidar\supercooledwaterday-hourly\era5';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\supercooled_water_v4_depol01';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 参数设置 --------
% 过冷水温度范围（0°C到-40°C）
temp_max = 0;    % 最高温度 0°C
temp_min = -40;  % 最低温度 -40°C

% Depol阈值范围 - 设置为0.1进行测试
depol_thresholds = [0.1];

% Backscatter阈值范围 - 基于测试结果优化
backscatter_thresholds = 0.04:0.01:0.20;

% 正确的CloudMask参数（来自原程序）
cloudmask_params = struct();
cloudmask_params.F_threshold = 800;
cloudmask_params.F2_threshold = 16;
cloudmask_params.maxHeight = 15.0;
cloudmask_params.k = 6;

fprintf('过冷水识别参数 V4 (正确CloudMask实现 + 优化阈值):\n');
fprintf('  过冷水温度范围: %.1f°C 到 %.1f°C\n', temp_min, temp_max);
fprintf('  Depol阈值: %.1f (固定为0.1)\n', depol_thresholds);
fprintf('  Backscatter阈值: %.2f 到 %.2f (间隔0.01)\n', min(backscatter_thresholds), max(backscatter_thresholds));
fprintf('  CloudMask参数: F_th=%d, F2_th=%d, k=%d\n', cloudmask_params.F_threshold, cloudmask_params.F2_threshold, cloudmask_params.k);
fprintf('  注意: CloudMask用于识别云层区域，这些区域是我们的分析目标\n');
fprintf('  当前设置: Depol<0.1, BackScatter>0.04 (严格模式)\n');

%% -------- 查找数据文件 --------
depolFiles = dir(fullfile(lidarDir, 'Depol_*.csv'));
fprintf('\n找到 %d 个Depol文件\n', length(depolFiles));

if isempty(depolFiles)
    error('未找到Depol文件，请检查数据路径');
end

%% -------- 处理每个文件 --------
for fileIdx = 1:length(depolFiles)
    try
        % 获取文件信息
        depolFile = depolFiles(fileIdx).name;
        fprintf('\n处理文件 %d/%d: %s\n', fileIdx, length(depolFiles), depolFile);
        
        % 从文件名提取日期
        dateMatch = regexp(depolFile, 'Depol_(\d{8})', 'tokens');
        if isempty(dateMatch)
            fprintf('  警告: 无法从文件名解析日期，跳过\n');
            continue;
        end
        dateStr = dateMatch{1}{1};
        dateFormatted = [dateStr(1:4), '-', dateStr(5:6), '-', dateStr(7:8)];
        
        % 查找对应文件
        backscatterFile = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', dateStr));
        backscatterFiles = dir(backscatterFile);
        if isempty(backscatterFiles)
            fprintf('  警告: 找不到对应的BackScatter文件，跳过\n');
            continue;
        end
        
        era5File = fullfile(era5Dir, sprintf('era5_temp_geopotential_%s.nc', dateStr));
        if ~exist(era5File, 'file')
            fprintf('  警告: 找不到对应的ERA5文件，跳过\n');
            continue;
        end
        
        fprintf('  找到所有必需文件，开始处理...\n');
        
        % 加载数据
        [lidar_data, success] = load_lidar_data_v4(lidarDir, backscatterDir, depolFile, backscatterFiles(1).name);
        if ~success
            fprintf('  错误: 激光雷达数据加载失败\n');
            continue;
        end
        
        [era5_data, success] = load_era5_data_v4(era5File);
        if ~success
            fprintf('  错误: ERA5数据加载失败\n');
            continue;
        end
        
        % 正确的CloudMask云识别
        [cloud_mask, cloud_stats] = identify_clouds_correct_cloudmask(lidar_data, cloudmask_params);
        fprintf('  正确CloudMask云识别完成，云覆盖率: %.1f%%\n', sum(cloud_mask(:))/numel(cloud_mask)*100);
        fprintf('    - 通过F/F2阈值检测: %d\n', cloud_stats.threshold_detections);
        fprintf('    - 通过水云条件检测: %d\n', cloud_stats.water_cloud_detections);
        fprintf('    - 通过梯度条件检测: %d\n', cloud_stats.gradient_detections);
        
        % 温度约束
        [temp_mask, height_info] = apply_temperature_constraint_v4(lidar_data, era5_data, temp_min, temp_max);
        fprintf('  温度约束完成，过冷区域覆盖率: %.1f%%\n', sum(temp_mask(:))/numel(temp_mask)*100);
        
        % 综合约束条件：云层区域 AND 过冷温度区域
        base_mask = cloud_mask & temp_mask;
        fprintf('  综合约束后覆盖率: %.1f%%\n', sum(base_mask(:))/numel(base_mask)*100);
        
        % 过冷水识别分析
        results = analyze_supercooled_water_v4(lidar_data, base_mask, depol_thresholds, backscatter_thresholds);
        
        % 生成过冷水识别结果图（只显示识别到的区域）
        generate_supercooled_water_plot_v4(lidar_data, era5_data, results, cloud_mask, cloud_stats, height_info, dateFormatted, outputDir, cloudmask_params);

        % 生成简化参数报告
        generate_simple_report_v4(results, height_info, dateFormatted, outputDir);
        
        fprintf('  处理完成: %s\n', dateFormatted);
        
    catch ME
        fprintf('  处理文件 %s 时出错: %s\n', depolFile, ME.message);
        continue;
    end
end

fprintf('\n所有文件处理完成！\n');
fprintf('结果保存在: %s\n', outputDir);

end

%% -------- 子函数：加载激光雷达数据 --------
function [lidar_data, success] = load_lidar_data_v4(lidarDir, backscatterDir, depolFile, backscatterFile)
    success = false;
    lidar_data = struct();
    
    try
        % 加载数据
        depolPath = fullfile(lidarDir, depolFile);
        raw_depol = readmatrix(depolPath);
        
        backscatterPath = fullfile(backscatterDir, backscatterFile);
        raw_backscatter = readmatrix(backscatterPath);
        
        % 处理高度数据
        height = raw_depol(:,1);
        valid_idx = ~isnan(height);
        height = height(valid_idx);
        
        % 限制高度范围到4km以内
        height_mask = height <= 4000;
        height = height(height_mask);
        
        % 获取完整的24小时数据
        max_time_points = 1440;  % 24小时的数据 (1440分钟)
        depol_data = raw_depol(valid_idx, 2:min(end, max_time_points+1));
        depol_data = depol_data(height_mask, :);

        backscatter_data = raw_backscatter(valid_idx, 2:min(end, max_time_points+1));
        backscatter_data = backscatter_data(height_mask, :);
        
        % 创建时间向量
        num_times = size(depol_data, 2);
        time_vector = 0:(num_times-1);
        
        % 存储数据
        lidar_data.height = height;
        lidar_data.time = time_vector;
        lidar_data.depol = double(depol_data);
        lidar_data.backscatter = double(backscatter_data);
        
        fprintf('    激光雷达数据加载成功\n');
        fprintf('    高度范围: %.0f - %.0f m\n', min(height), max(height));
        fprintf('    时间范围: %d 分钟\n', num_times);
        
        success = true;
        
    catch ME
        fprintf('    激光雷达数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：加载ERA5数据 --------
function [era5_data, success] = load_era5_data_v4(era5File)
    success = false;
    era5_data = struct();
    
    try
        % 读取ERA5数据
        temperature = ncread(era5File, 't');
        geopotential = ncread(era5File, 'z');
        pressure_levels = ncread(era5File, 'pressure_level');
        time = ncread(era5File, 'valid_time');
        
        % 转换单位
        temperature_celsius = squeeze(temperature) - 273.15;
        geopotential_height = squeeze(geopotential) / 9.80665;
        
        % 存储数据
        era5_data.temperature = temperature_celsius;
        era5_data.height = geopotential_height;
        era5_data.pressure = pressure_levels;
        era5_data.time = time;
        
        fprintf('    ERA5数据加载成功\n');
        fprintf('    温度范围: %.1f - %.1f °C\n', min(temperature_celsius(:)), max(temperature_celsius(:)));
        fprintf('    高度范围: %.0f - %.0f m\n', min(geopotential_height(:)), max(geopotential_height(:)));
        
        success = true;
        
    catch ME
        fprintf('    ERA5数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：正确的CloudMask云识别算法 --------
function [cloud_mask, cloud_stats] = identify_clouds_correct_cloudmask(lidar_data, params)
    fprintf('    应用正确的CloudMask算法进行云识别...\n');
    fprintf('    注意: CloudMask识别的是云层区域，这些是我们要分析的目标\n');

    % 获取数据
    Height = lidar_data.height;
    Backscatter = lidar_data.backscatter;

    % 转换高度为km
    height_km = Height / 1000;
    maxHeight = params.maxHeight;

    % 获取数据维度 - Backscatter是 (高度 x 时间)
    [Nheight, Ntime] = size(Backscatter);
    fprintf('    Backscatter维度: %d x %d (高度 x 时间)\n', Nheight, Ntime);

    % 截取高度小于等于maxHeight的部分
    z_idx = height_km <= maxHeight;
    if sum(z_idx) == 0
        z_idx = ones(size(height_km), 'logical');
    end
    z = height_km(z_idx);
    Nz = length(z);
    fprintf('    使用高度范围: 0 - %.1f km, 共 %d 个高度点\n', maxHeight, Nz);

    % 初始化云标记矩阵 - cloud(时间, 高度)
    cloud = zeros(Ntime, Nz);

    % 统计变量
    cloud_detected_times = 0;
    threshold_detections = 0;
    water_cloud_detections = 0;
    gradient_detections = 0;

    % 遍历每个时间点
    for itime = 1:Ntime
        % 显示进度
        if mod(itime, 100) == 1
            fprintf('      处理时间点 %d/%d...\n', itime, Ntime);
        end

        % 计算未经距离校正的信号
        height_km_safe = height_km;
        height_km_safe(height_km_safe < 0.01) = 0.01;
        P = Backscatter(:, itime) ./ (height_km_safe.^2);
        PM = Backscatter(:, itime);

        % 移除无穷大和NaN值
        P(~isfinite(P)) = 0;
        PM(~isfinite(PM)) = 0;

        % 计算噪声水平
        Pnoise_idx = height_km >= maxHeight;
        if any(Pnoise_idx) && sum(Pnoise_idx) > 10
            Pnoise = P(Pnoise_idx);
            Pnoise = Pnoise(isfinite(Pnoise));
            if length(Pnoise) > 5
                sd = std(Pnoise, 'omitnan');
            else
                sd = std(P(end-50:end), 'omitnan');
            end
        else
            sd = std(P(end-50:end), 'omitnan');
        end

        % 确保sd有效且不为零
        if isnan(sd) || sd == 0
            sd = mean(P(P > 0), 'omitnan') * 0.1;
        end

        % 检查SD是否为NaN
        if isnan(sd)
            cloud(itime, :) = NaN;
            continue;
        end

        % 设置噪声阈值
        k = params.k;
        noise = k * sd;

        % 获取高度小于等于maxHeight的信号
        P_max_height = P(z_idx);

        % 平滑信号
        Ps = movmean(P_max_height, 3);

        % 初始化前向和后向处理的数据
        PD1 = Ps;
        PD2 = Ps;

        % 前向扫描处理
        for zi = 1:(Nz-1)
            if ~(abs(PD1(zi+1) - PD1(zi)) >= noise)
                PD1(zi+1) = PD1(zi);
            end
        end

        % 后向扫描处理
        for zi = 1:(Nz-1)
            if ~(abs(PD2(Nz-zi) - PD2(Nz-zi+1)) >= noise)
                PD2(Nz-zi) = PD2(Nz-zi+1);
            end
        end

        % 检查PD1和PD2是否有效
        if all(isnan(PD1)) || all(isnan(PD2))
            cloud(itime, :) = NaN;
            continue;
        end

        % 计算平均值
        PD = (PD1 + PD2) / 2;

        % 应用CloudMask层检测算法
        [layer_result, layer_stats] = apply_correct_cloudmask_detection(PD, z, PM, params);
        cloud(itime, :) = layer_result;

        % 更新统计
        if any(layer_result)
            cloud_detected_times = cloud_detected_times + 1;
        end
        threshold_detections = threshold_detections + layer_stats.threshold_count;
        water_cloud_detections = water_cloud_detections + layer_stats.water_cloud_count;
        gradient_detections = gradient_detections + layer_stats.gradient_count;
    end

    % 创建完整的云层掩码矩阵，与激光雷达数据维度匹配
    cloud_mask = false(size(Backscatter));  % (高度 x 时间)
    z_indices = find(z_idx);

    for itime = 1:min(Ntime, size(Backscatter, 2))
        for iz = 1:min(Nz, length(z_indices))
            if iz <= size(cloud, 2) && itime <= size(cloud, 1)
                original_height_idx = z_indices(iz);
                if original_height_idx <= size(cloud_mask, 1)
                    cloud_mask(original_height_idx, itime) = cloud(itime, iz);
                end
            end
        end
    end

    % 返回统计信息
    cloud_stats = struct();
    cloud_stats.total_detections = cloud_detected_times;
    cloud_stats.threshold_detections = threshold_detections;
    cloud_stats.water_cloud_detections = water_cloud_detections;
    cloud_stats.gradient_detections = gradient_detections;

    fprintf('    正确CloudMask处理完成\n');
end

%% -------- 子函数：正确的CloudMask层检测算法 --------
function [cloud_result, layer_stats] = apply_correct_cloudmask_detection(PD, z, PM, params)
    % 正确的CloudMask层检测算法（完全按照原程序实现）

    Nz = length(z);
    cloud_result = zeros(1, Nz);

    % 初始化统计
    layer_stats = struct();
    layer_stats.threshold_count = 0;
    layer_stats.water_cloud_count = 0;
    layer_stats.gradient_count = 0;

    % 按升序排列
    [Rs, Is] = sort(PD);
    MA = max(Rs, [], 'omitnan');
    MI = min(Rs, [], 'omitnan');
    PE = (1:Nz) / Nz;

    % 处理相等值
    for i = 1:(Nz-1)
        if Rs(i+1) == Rs(i)
            PE(i+1) = PE(i);
        end
    end

    % 计算y值
    y = PE .* (MA - MI) + MI;

    % 初始化PN
    PN = zeros(1, Nz);
    PN(Is) = y;

    % 计算基线
    B = ((Nz:-1:1) / Nz) .* (MA - MI) + MI;

    % 初始化存储数组
    base = [];
    top = [];
    Area = [];

    % 检测层边界
    for zi = 1:(Nz-1)
        if ~(PN(zi+1) > B(zi+1) && PN(zi) <= B(zi))
            continue;
        end

        s = 0;
        if PN(zi+1) > PN(zi)
            s = PN(zi+1) - B(zi+1);
        end

        for i = 1:(Nz-zi-2)
            if zi+i+2 <= Nz
                if PN(zi+i+2) > B(zi+i+2)
                    s = s + PN(zi+i+2) - B(zi+i+2);
                end

                if PN(zi+i+2) <= B(zi+i+2)
                    if i+1 >= 3
                        base = [base, zi+1];
                        top = [top, zi+i+2];
                        if s <= (B(zi+1) - B(zi+i+2)) * (i+1) / 2
                            s = 0;
                        end
                        Area = [Area, s];
                    end
                    break;
                end
            else
                break;
            end
        end
    end

    % 获取层数
    Nlayer = length(base);

    if Nlayer == 0
        return;
    end

    % 计算对数NRB
    if any(PN(:))
        ln_nrb = log(PN .* (z.^2));
    else
        ln_nrb = zeros(size(PN));
    end

    % 初始化梯度
    G = zeros(1, Nz);

    % 计算梯度
    for zi = 1:(Nz-2)
        if (z(zi+2) - z(zi)) > 0
            G(zi+1) = (ln_nrb(zi+2) - ln_nrb(zi)) / (z(zi+2) - z(zi));
        else
            G(zi+1) = 0;
        end
    end

    % 检测云层
    for ilayer = 1:Nlayer
        % 计算F值
        F1 = 100 * (PN - B) / MA;
        PMX = 0;

        for i = 1:base(ilayer)
            PMX = PMX + PM(i);
        end

        F = Area(ilayer) * PMX / (z(top(ilayer)) - z(base(ilayer))) / MA;
        F2 = max(F1(base(ilayer):top(ilayer)+1), [], 'omitnan');

        % 标记是否已经被检测为云层
        already_detected = false;

        % 根据阈值判断云层
        if F > params.F_threshold || F2 > params.F2_threshold
            cloud_result(base(ilayer):top(ilayer)+1) = 1;
            layer_stats.threshold_count = layer_stats.threshold_count + 1;
            already_detected = true;
        end

        % 检测2km以下的水云
        if (z(base(ilayer)) < 2) && (median(PM(base(ilayer):top(ilayer)+1), 'omitnan') > 0.1) && (prctile(PM(base(ilayer):top(ilayer)+1), 90) > 1)
            if F > 1 && ~already_detected
                cloud_result(base(ilayer):top(ilayer)+1) = 1;
                layer_stats.water_cloud_count = layer_stats.water_cloud_count + 1;
                already_detected = true;
            end
            if (max(G(base(ilayer)+1:top(ilayer)+1), [], 'omitnan') > 3 || min(G(base(ilayer)+1:top(ilayer)+1), [], 'omitnan') < -7) && ~already_detected
                cloud_result(base(ilayer):top(ilayer)+1) = 1;
                layer_stats.gradient_count = layer_stats.gradient_count + 1;
            end
        end
    end
end

%% -------- 子函数：温度约束 --------
function [temp_mask, height_info] = apply_temperature_constraint_v4(lidar_data, era5_data, temp_min, temp_max)
    fprintf('    应用过冷水温度约束 (%.1f°C 到 %.1f°C)...\n', temp_min, temp_max);

    % 初始化温度掩码
    temp_mask = false(size(lidar_data.depol));

    % 统计变量
    total_points = 0;
    supercooled_points = 0;

    % 计算平均温度剖面来确定过冷水高度范围
    mean_temp_profile = mean(era5_data.temperature, 2);
    mean_height_profile = mean(era5_data.height, 2);

    % 插值到激光雷达高度网格
    temp_interp_mean = interp1(mean_height_profile, mean_temp_profile, lidar_data.height, 'linear', 'extrap');

    % 找到过冷水温度范围对应的高度范围
    supercooled_indices = find((temp_interp_mean >= temp_min) & (temp_interp_mean <= temp_max));

    if ~isempty(supercooled_indices)
        supercooled_height_min = lidar_data.height(min(supercooled_indices));
        supercooled_height_max = lidar_data.height(max(supercooled_indices));

        % 找到0°C和-40°C对应的具体高度
        [~, zero_idx] = min(abs(temp_interp_mean - 0));
        [~, minus40_idx] = min(abs(temp_interp_mean - (-40)));

        zero_height = lidar_data.height(zero_idx);
        minus40_height = lidar_data.height(minus40_idx);

        height_info = struct();
        height_info.supercooled_range = [supercooled_height_min, supercooled_height_max];
        height_info.zero_height = zero_height;
        height_info.minus40_height = minus40_height;
        height_info.temp_profile = temp_interp_mean;

        fprintf('    过冷水高度范围: %.0f - %.0f m\n', supercooled_height_min, supercooled_height_max);
        fprintf('    0°C高度: %.0f m, -40°C高度: %.0f m\n', zero_height, minus40_height);
    else
        height_info = struct();
        height_info.supercooled_range = [NaN, NaN];
        height_info.zero_height = NaN;
        height_info.minus40_height = NaN;
        height_info.temp_profile = temp_interp_mean;

        fprintf('    警告: 在激光雷达高度范围内未找到过冷水温度区域\n');
    end

    % 对每个时间点进行温度插值
    for t = 1:length(lidar_data.time)
        % 选择最接近的ERA5时间点
        era5_time_idx = min(t, size(era5_data.temperature, 2));

        % 获取当前时间的温度和高度剖面
        temp_profile = era5_data.temperature(:, era5_time_idx);
        height_profile = era5_data.height(:, era5_time_idx);

        % 插值到激光雷达高度网格
        temp_interp = interp1(height_profile, temp_profile, lidar_data.height, 'linear', 'extrap');

        % 应用过冷水温度约束：温度在-40°C到0°C之间
        temp_condition = (temp_interp >= temp_min) & (temp_interp <= temp_max);
        temp_mask(:, t) = temp_condition;

        % 统计
        total_points = total_points + length(temp_interp);
        supercooled_points = supercooled_points + sum(temp_condition);
    end

    % 输出统计信息
    supercooled_percentage = supercooled_points / total_points * 100;
    fprintf('    过冷水温度约束完成\n');
    fprintf('    过冷温度范围内的数据点: %d/%d (%.1f%%)\n', ...
            supercooled_points, total_points, supercooled_percentage);

    % 显示激光雷达高度层的温度范围
    fprintf('    激光雷达高度层温度范围: %.1f°C 到 %.1f°C\n', ...
            min(temp_interp_mean), max(temp_interp_mean));
end

%% -------- 子函数：过冷水识别分析 --------
function results = analyze_supercooled_water_v4(lidar_data, base_mask, depol_thresholds, backscatter_thresholds)
    fprintf('    开始过冷水识别分析...\n');

    results = struct();
    results.depol_thresholds = depol_thresholds;
    results.backscatter_thresholds = backscatter_thresholds;
    results.detection_matrix = zeros(length(depol_thresholds), length(backscatter_thresholds));
    results.coverage_matrix = zeros(length(depol_thresholds), length(backscatter_thresholds));

    for i = 1:length(depol_thresholds)
        for j = 1:length(backscatter_thresholds)
            depol_thresh = depol_thresholds(i);
            backscatter_thresh = backscatter_thresholds(j);

            % 应用阈值条件
            depol_condition = lidar_data.depol < depol_thresh;
            backscatter_condition = lidar_data.backscatter > backscatter_thresh;

            % 综合所有条件
            supercooled_mask = base_mask & depol_condition & backscatter_condition;

            % 计算识别像素数和覆盖率
            detection_count = sum(supercooled_mask(:));
            if sum(base_mask(:)) > 0
                coverage_rate = detection_count / sum(base_mask(:)) * 100;
            else
                coverage_rate = 0;
            end

            results.detection_matrix(i, j) = detection_count;
            results.coverage_matrix(i, j) = coverage_rate;
        end
    end

    % 找到最优阈值组合
    [max_coverage, max_idx] = max(results.coverage_matrix(:));
    [opt_i, opt_j] = ind2sub(size(results.coverage_matrix), max_idx);

    results.optimal_depol = depol_thresholds(opt_i);
    results.optimal_backscatter = backscatter_thresholds(opt_j);
    results.max_coverage = max_coverage;

    fprintf('    最优阈值组合: Depol < %.2f, Backscatter > %.2f\n', ...
            results.optimal_depol, results.optimal_backscatter);
    fprintf('    最大覆盖率: %.1f%%\n', results.max_coverage);
end

%% -------- 子函数：生成识别结果图 --------
function generate_detection_plots_v4(lidar_data, era5_data, results, cloud_mask, cloud_stats, height_info, dateStr, outputDir, cloudmask_params)
    fprintf('    生成识别结果图...\n');

    % 创建大图
    fig = figure('Position', [100, 100, 1800, 1200]);

    % 时间轴（转换为小时）
    time_hours = lidar_data.time / 60;

    % 子图1: 原始Depol数据
    subplot(2, 4, 1);
    pcolor(time_hours, lidar_data.height/1000, lidar_data.depol);
    shading flat;
    colorbar;
    clim([0, 1]);
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('退偏振比 (Depol)');
    ylim([0, 4]);

    % 子图2: 原始BackScatter数据
    subplot(2, 4, 2);
    pcolor(time_hours, lidar_data.height/1000, log10(lidar_data.backscatter + 1e-6));
    shading flat;
    colorbar;
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('后向散射 (log10)');
    ylim([0, 4]);

    % 子图3: CloudMask识别的云层
    subplot(2, 4, 3);
    pcolor(time_hours, lidar_data.height/1000, double(cloud_mask));
    shading flat;
    colormap(gca, [1 1 1; 0 0.8 1]);  % 白色背景，浅蓝色云层
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title('CloudMask识别的云层');
    ylim([0, 4]);

    % 子图4: ERA5温度剖面
    subplot(2, 4, 4);
    mean_temp = mean(era5_data.temperature, 2);
    mean_height = mean(era5_data.height, 2);
    plot(mean_temp, mean_height/1000, 'b-o', 'LineWidth', 2);
    grid on;
    xlabel('温度 (°C)');
    ylabel('高度 (km)');

    % 显示具体的过冷水高度范围
    if ~isnan(height_info.supercooled_range(1))
        title_str = sprintf('ERA5温度剖面\n过冷水高度: %.0f-%.0f m', ...
                           height_info.supercooled_range(1), height_info.supercooled_range(2));

        % 标记过冷水高度范围
        hold on;
        fill([-50, 10, 10, -50], ...
             [height_info.supercooled_range(1)/1000, height_info.supercooled_range(1)/1000, ...
              height_info.supercooled_range(2)/1000, height_info.supercooled_range(2)/1000], ...
             'cyan', 'FaceAlpha', 0.3);

        % 标记0°C和-40°C高度线
        plot([-50, 10], [height_info.zero_height/1000, height_info.zero_height/1000], 'r--', 'LineWidth', 1);
        plot([-50, 10], [height_info.minus40_height/1000, height_info.minus40_height/1000], 'r--', 'LineWidth', 1);

        % 添加标签
        text(-45, height_info.zero_height/1000 + 0.1, '0°C', 'Color', 'red', 'FontWeight', 'bold');
        text(-45, height_info.minus40_height/1000 + 0.1, '-40°C', 'Color', 'red', 'FontWeight', 'bold');

        legend('温度剖面', '过冷水高度范围', '0°C线', '-40°C线', 'Location', 'best');
    else
        title_str = 'ERA5温度剖面\n(无过冷水高度范围)';
        fill([-40, 0, 0, -40], [0, 0, 4, 4], 'cyan', 'FaceAlpha', 0.3);
        legend('温度剖面', '理论过冷水范围', 'Location', 'best');
    end

    title(title_str, 'FontSize', 10);
    xlim([-50, 10]);
    ylim([0, 4]);

    % 子图5: 阈值分析热图
    subplot(2, 4, 5);
    imagesc(results.backscatter_thresholds, results.depol_thresholds, results.coverage_matrix);
    colorbar;
    xlabel('BackScatter阈值');
    ylabel('Depol阈值');
    title('覆盖率分析 (%)');
    set(gca, 'YDir', 'normal');
    hold on;
    plot(results.optimal_backscatter, results.optimal_depol, 'r*', 'MarkerSize', 15, 'LineWidth', 3);

    % 子图6: 最优阈值识别结果
    subplot(2, 4, 6);
    depol_condition = lidar_data.depol < results.optimal_depol;
    backscatter_condition = lidar_data.backscatter > results.optimal_backscatter;

    % 综合条件：云层 + 过冷温度 + 阈值条件
    temp_mask_simple = true(size(cloud_mask));  % 简化显示，假设所有高度都在过冷范围
    supercooled_mask = cloud_mask & temp_mask_simple & depol_condition & backscatter_condition;

    pcolor(time_hours, lidar_data.height/1000, double(supercooled_mask));
    shading flat;
    colormap(gca, [1 1 1; 0 0.8 0.8]);
    xlabel('时间 (小时)');
    ylabel('高度 (km)');
    title(sprintf('过冷水识别结果\n(Depol<%.2f, BS>%.2f)', ...
                  results.optimal_depol, results.optimal_backscatter));
    ylim([0, 4]);

    % 子图7: CloudMask统计信息
    subplot(2, 4, 7);
    detection_types = {'F/F2阈值', '水云条件', '梯度条件'};
    detection_counts = [cloud_stats.threshold_detections, cloud_stats.water_cloud_detections, cloud_stats.gradient_detections];

    bar(detection_counts, 'FaceColor', [0.3 0.6 0.9]);
    set(gca, 'XTickLabel', detection_types);
    ylabel('检测次数');
    title('CloudMask检测统计');
    grid on;

    % 添加数值标签
    for i = 1:length(detection_counts)
        if detection_counts(i) > 0
            text(i, detection_counts(i) + max(detection_counts)*0.02, num2str(detection_counts(i)), ...
                 'HorizontalAlignment', 'center', 'FontWeight', 'bold');
        end
    end

    % 子图8: 统计信息
    subplot(2, 4, 8);
    axis off;

    % 构建统计信息文本
    stats_text = {
        sprintf('日期: %s (24小时)', dateStr);
        '';
        '过冷水高度范围:';
    };

    if ~isnan(height_info.supercooled_range(1))
        stats_text = [stats_text; {
            sprintf('  过冷水高度: %.0f-%.0f m', height_info.supercooled_range(1), height_info.supercooled_range(2));
            sprintf('  0°C高度: %.0f m', height_info.zero_height);
            sprintf('  -40°C高度: %.0f m', height_info.minus40_height);
        }];
    else
        stats_text = [stats_text; {'  无过冷水高度范围'}];
    end

    stats_text = [stats_text; {
        '';
        '正确CloudMask参数:';
        sprintf('  F阈值: %d', cloudmask_params.F_threshold);
        sprintf('  F2阈值: %d', cloudmask_params.F2_threshold);
        sprintf('  噪声系数k: %d', cloudmask_params.k);
        '';
        'CloudMask检测统计:';
        sprintf('  F/F2阈值检测: %d', cloud_stats.threshold_detections);
        sprintf('  水云条件检测: %d', cloud_stats.water_cloud_detections);
        sprintf('  梯度条件检测: %d', cloud_stats.gradient_detections);
        sprintf('  云覆盖率: %.1f%%', sum(cloud_mask(:))/numel(cloud_mask)*100);
        '';
        '识别参数:';
        sprintf('  最优Depol阈值: %.2f', results.optimal_depol);
        sprintf('  最优BackScatter阈值: %.2f', results.optimal_backscatter);
        sprintf('  最大覆盖率: %.1f%%', results.max_coverage);
    }];

    text(0.1, 0.9, stats_text, 'FontSize', 9, 'VerticalAlignment', 'top', ...
         'HorizontalAlignment', 'left', 'Units', 'normalized');

    % 添加总标题
    sgtitle(sprintf('过冷水识别分析 V4 (正确CloudMask, 24小时) - %s', dateStr), 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, sprintf('supercooled_water_detection_v4_%s.png', strrep(dateStr, '-', '')));
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('    保存图像: %s\n', outputFile);

    close(fig);
end

%% -------- 子函数：保存结果数据 --------
function save_results_v4(results, lidar_data, era5_data, cloud_stats, height_info, dateStr, outputDir, cloudmask_params)
    % 保存分析结果到MAT文件

    analysis_results = struct();
    analysis_results.version = 'V4_Correct_CloudMask_24h';
    analysis_results.date = dateStr;
    analysis_results.cloudmask_params = cloudmask_params;
    analysis_results.cloud_stats = cloud_stats;
    analysis_results.height_info = height_info;  % 添加高度信息
    analysis_results.optimal_depol_threshold = results.optimal_depol;
    analysis_results.optimal_backscatter_threshold = results.optimal_backscatter;
    analysis_results.max_coverage_rate = results.max_coverage;
    analysis_results.depol_thresholds = results.depol_thresholds;
    analysis_results.backscatter_thresholds = results.backscatter_thresholds;
    analysis_results.coverage_matrix = results.coverage_matrix;
    analysis_results.detection_matrix = results.detection_matrix;

    % 保存原始数据摘要
    analysis_results.lidar_summary = struct();
    analysis_results.lidar_summary.height_range = [min(lidar_data.height), max(lidar_data.height)];
    analysis_results.lidar_summary.time_range = [min(lidar_data.time), max(lidar_data.time)];
    analysis_results.lidar_summary.depol_range = [min(lidar_data.depol(:)), max(lidar_data.depol(:))];
    analysis_results.lidar_summary.backscatter_range = [min(lidar_data.backscatter(:)), max(lidar_data.backscatter(:))];

    analysis_results.era5_summary = struct();
    analysis_results.era5_summary.temperature_range = [min(era5_data.temperature(:)), max(era5_data.temperature(:))];
    analysis_results.era5_summary.height_range = [min(era5_data.height(:)), max(era5_data.height(:))];

    % 保存文件
    outputFile = fullfile(outputDir, sprintf('supercooled_water_results_v4_%s.mat', strrep(dateStr, '-', '')));
    save(outputFile, 'analysis_results');
    fprintf('    保存结果数据: %s\n', outputFile);
end

%% -------- 子函数：生成过冷水识别结果图（只显示识别区域）--------
function generate_supercooled_water_plot_v4(lidar_data, era5_data, results, cloud_mask, cloud_stats, height_info, dateStr, outputDir, cloudmask_params)
    fprintf('    生成过冷水识别结果图（只显示识别区域）...\n');

    % 创建图形 - 加长x轴
    fig = figure('Position', [100, 100, 1600, 800]);

    % 时间轴（转换为小时）
    time_hours = lidar_data.time / 60;

    % 计算最优阈值的过冷水识别结果
    temp_mask_simple = true(size(cloud_mask));  % 简化显示，假设所有高度都在过冷范围
    depol_condition = lidar_data.depol < results.optimal_depol;
    backscatter_condition = lidar_data.backscatter > results.optimal_backscatter;

    % 综合条件：云层 + 过冷温度 + 阈值条件
    supercooled_mask = cloud_mask & temp_mask_simple & depol_condition & backscatter_condition;

    % 创建显示矩阵：只显示识别到的过冷水区域
    display_matrix = zeros(size(supercooled_mask));
    display_matrix(supercooled_mask) = 1;  % 过冷水区域设为1

    % 绘制过冷水识别结果
    pcolor(time_hours, lidar_data.height/1000, display_matrix);
    shading flat;

    % 设置颜色映射：白色背景，深蓝色表示过冷水
    colormap([1 1 1; 0 0 0.8]);  % 白色背景，深蓝色过冷水

    % 设置坐标轴
    xlabel('时间 (小时)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('高度 (km)', 'FontSize', 12, 'FontWeight', 'bold');

    % 设置x轴范围为24小时
    xlim([0, 24]);
    ylim([0, 4]);

    % 设置网格
    grid on;
    set(gca, 'GridAlpha', 0.3);

    % 添加标题
    if ~isnan(height_info.supercooled_range(1))
        title_str = sprintf('过冷水识别结果 - %s\n过冷水高度: %.0f-%.0f m | Depol<%.2f, BackScatter>%.2f | 覆盖率: %.1f%%', ...
                           dateStr, height_info.supercooled_range(1), height_info.supercooled_range(2), ...
                           results.optimal_depol, results.optimal_backscatter, results.max_coverage);
    else
        title_str = sprintf('过冷水识别结果 - %s\nDepol<%.2f, BackScatter>%.2f | 覆盖率: %.1f%%', ...
                           dateStr, results.optimal_depol, results.optimal_backscatter, results.max_coverage);
    end
    title(title_str, 'FontSize', 14, 'FontWeight', 'bold');

    % 添加颜色条
    cb = colorbar;
    cb.Ticks = [0, 1];
    cb.TickLabels = {'无过冷水', '过冷水'};
    cb.Label.String = '识别结果';
    cb.Label.FontSize = 12;
    cb.Label.FontWeight = 'bold';

    % 添加统计信息文本框
    stats_text = {
        sprintf('识别统计:');
        sprintf('总像素数: %d', numel(supercooled_mask));
        sprintf('过冷水像素: %d', sum(supercooled_mask(:)));
        sprintf('覆盖率: %.2f%%', results.max_coverage);
        '';
        sprintf('参数设置:');
        sprintf('Depol阈值: < %.2f', results.optimal_depol);
        sprintf('BackScatter阈值: > %.2f', results.optimal_backscatter);
        sprintf('温度范围: -40°C ~ 0°C');
    };

    % 在图的右上角添加统计信息
    annotation('textbox', [0.75, 0.75, 0.2, 0.2], 'String', stats_text, ...
               'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
               'FitBoxToText', 'on');

    % 保存图像
    outputFile = fullfile(outputDir, sprintf('supercooled_water_%s.png', strrep(dateStr, '-', '')));
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('    保存过冷水识别图: %s\n', outputFile);

    close(fig);
end

%% -------- 子函数：生成简化参数报告 --------
function generate_simple_report_v4(results, height_info, dateStr, outputDir)
    % 创建简化报告文件
    reportFile = fullfile(outputDir, sprintf('report_%s.txt', strrep(dateStr, '-', '')));

    fid = fopen(reportFile, 'w');
    if fid == -1
        return;
    end

    try
        fprintf(fid, '%s\n', dateStr);

        if ~isnan(height_info.supercooled_range(1))
            fprintf(fid, '过冷水高度: %.0f-%.0f m\n', height_info.supercooled_range(1), height_info.supercooled_range(2));
            fprintf(fid, '0°C高度: %.0f m\n', height_info.zero_height);
            fprintf(fid, '-40°C高度: %.0f m\n', height_info.minus40_height);
        else
            fprintf(fid, '过冷水高度: 无\n');
        end

        fprintf(fid, 'Depol阈值: %.2f\n', results.optimal_depol);
        fprintf(fid, 'BackScatter阈值: %.2f\n', results.optimal_backscatter);
        fprintf(fid, '覆盖率: %.1f%%\n', results.max_coverage);

        fclose(fid);

    catch ME
        fclose(fid);
    end
end
