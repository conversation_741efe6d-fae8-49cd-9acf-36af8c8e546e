%% 测试雷达回波与过冷水叠加程序
% 这个脚本用于测试radar_supercooled_water_overlay.m程序

clc; clear; close all;

%% 检查必要的目录和文件
fprintf('=== 测试雷达回波与过冷水叠加程序 ===\n\n');

% 设置路径
radarDir = 'D:\dataset\shandong';
supercooledDir = 'D:\lidar\supercooledwaterday-hourly\figure\supercooled_water_v4_depol01';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\radar_supercooled_overlay';

fprintf('1. 检查目录存在性:\n');
fprintf('   雷达数据目录: %s ... ', radarDir);
if exist(radarDir, 'dir')
    fprintf('存在\n');
else
    fprintf('不存在\n');
    error('雷达数据目录不存在');
end

fprintf('   过冷水结果目录: %s ... ', supercooledDir);
if exist(supercooledDir, 'dir')
    fprintf('存在\n');
else
    fprintf('不存在\n');
    error('过冷水结果目录不存在');
end

fprintf('   输出目录: %s ... ', outputDir);
if exist(outputDir, 'dir')
    fprintf('存在\n');
else
    fprintf('不存在，将创建\n');
    mkdir(outputDir);
end

%% 检查数据文件
fprintf('\n2. 检查数据文件:\n');

% 查找雷达数据文件
matFiles = dir(fullfile(radarDir, '*.mat'));
fprintf('   找到雷达数据文件: %d 个\n', length(matFiles));

if isempty(matFiles)
    error('未找到雷达数据文件');
end

% 显示前几个文件
fprintf('   前5个雷达文件:\n');
for i = 1:min(5, length(matFiles))
    fprintf('     %s\n', matFiles(i).name);
end

% 查找过冷水识别结果文件
supercooledFiles = dir(fullfile(supercooledDir, 'supercooled_data_*.mat'));
fprintf('   找到过冷水识别结果文件: %d 个\n', length(supercooledFiles));

if isempty(supercooledFiles)
    error('未找到过冷水识别结果文件');
end

% 显示前几个文件
fprintf('   前5个过冷水结果文件:\n');
for i = 1:min(5, length(supercooledFiles))
    fprintf('     %s\n', supercooledFiles(i).name);
end

%% 检查文件匹配性
fprintf('\n3. 检查文件匹配性:\n');

matched_count = 0;
for i = 1:length(matFiles)
    % 从雷达文件名提取日期
    [~, matFileName, ~] = fileparts(matFiles(i).name);
    dateStr = strrep(matFileName, '_reprocessed', '');
    
    % 转换为yyyymmdd格式
    dateParts = split(dateStr, '-');
    if length(dateParts) == 3 && strcmp(dateParts{2}, '02')  % 只检查2月数据
        yyyymmdd = strcat(dateParts{1}, dateParts{2}, dateParts{3});
        
        % 查找对应的过冷水文件
        supercooledFile = fullfile(supercooledDir, sprintf('supercooled_data_%s.mat', yyyymmdd));
        if exist(supercooledFile, 'file')
            matched_count = matched_count + 1;
            if matched_count <= 3  % 只显示前3个匹配的
                fprintf('   匹配: %s <-> supercooled_data_%s.mat\n', dateStr, yyyymmdd);
            end
        end
    end
end

fprintf('   总共找到匹配的文件对: %d 对\n', matched_count);

if matched_count == 0
    error('未找到匹配的文件对');
end

%% 测试加载一个过冷水识别结果文件
fprintf('\n4. 测试过冷水数据加载:\n');

% 选择第一个过冷水文件进行测试
testFile = fullfile(supercooledDir, supercooledFiles(1).name);
fprintf('   测试文件: %s\n', supercooledFiles(1).name);

try
    testData = load(testFile);
    if isfield(testData, 'supercooled_data')
        supercooled_info = testData.supercooled_data;
        fprintf('   ✓ 成功加载过冷水数据\n');
        
        % 检查必要字段
        required_fields = {'supercooled_mask', 'time_hours', 'height_km', 'optimal_depol', 'optimal_backscatter', 'coverage_rate'};
        for j = 1:length(required_fields)
            if isfield(supercooled_info, required_fields{j})
                fprintf('   ✓ 字段 %s 存在\n', required_fields{j});
            else
                fprintf('   ✗ 字段 %s 缺失\n', required_fields{j});
            end
        end
        
        % 显示数据信息
        fprintf('   数据维度:\n');
        fprintf('     过冷水掩码: %d x %d\n', size(supercooled_info.supercooled_mask));
        fprintf('     时间点数: %d\n', length(supercooled_info.time_hours));
        fprintf('     高度点数: %d\n', length(supercooled_info.height_km));
        fprintf('   识别参数:\n');
        fprintf('     最优Depol阈值: %.2f\n', supercooled_info.optimal_depol);
        fprintf('     最优BackScatter阈值: %.2f\n', supercooled_info.optimal_backscatter);
        fprintf('     覆盖率: %.1f%%\n', supercooled_info.coverage_rate);
        
    else
        error('过冷水数据文件中缺少supercooled_data字段');
    end
catch ME
    fprintf('   ✗ 加载过冷水数据失败: %s\n', ME.message);
    error('过冷水数据加载测试失败');
end

%% 测试程序运行
fprintf('\n5. 程序就绪状态:\n');
fprintf('   ✓ 所有必要目录存在\n');
fprintf('   ✓ 找到 %d 个雷达数据文件\n', length(matFiles));
fprintf('   ✓ 找到 %d 个过冷水识别结果文件\n', length(supercooledFiles));
fprintf('   ✓ 找到 %d 对匹配的文件\n', matched_count);
fprintf('   ✓ 过冷水数据格式正确\n');

fprintf('\n=== 测试完成，程序可以运行 ===\n');
fprintf('现在可以运行 radar_supercooled_water_overlay.m 程序\n');

%% 提供运行建议
fprintf('\n运行建议:\n');
fprintf('1. 直接运行: radar_supercooled_water_overlay\n');
fprintf('2. 或者在命令窗口输入: run(''radar_supercooled_water_overlay.m'')\n');
fprintf('3. 结果将保存在: %s\n', outputDir);
