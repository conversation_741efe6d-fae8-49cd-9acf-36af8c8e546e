function clustering_threshold_analysis()
% 基于聚类方法计算过冷水识别的最优阈值
% 分析Depol和BackScatter数据的分布特征，自动确定阈值

clc; clear; close all;

%% -------- 设置路径 --------
lidarDir = 'D:\lidar\supercooledwaterday-hourly';
backscatterDir = 'D:\lidar\backscatter';
era5Dir = 'D:\lidar\supercooledwaterday-hourly\era5';
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\clustering_analysis';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 参数设置 --------
% 过冷水温度范围
temp_max = 0;    % 最高温度 0°C
temp_min = -40;  % 最低温度 -40°C

% CloudMask参数
cloudmask_params = struct();
cloudmask_params.F_threshold = 800;
cloudmask_params.F2_threshold = 16;
cloudmask_params.maxHeight = 15.0;
cloudmask_params.k = 6;

fprintf('聚类阈值分析参数:\n');
fprintf('  过冷水温度范围: %.1f°C 到 %.1f°C\n', temp_min, temp_max);
fprintf('  目标: 通过聚类分析确定Depol和BackScatter阈值\n');

%% -------- 查找数据文件 --------
depolFiles = dir(fullfile(lidarDir, 'Depol_*.csv'));
fprintf('\n找到 %d 个Depol文件\n', length(depolFiles));

if isempty(depolFiles)
    error('未找到Depol文件，请检查数据路径');
end

% 分析全部文件
test_files = length(depolFiles);  % 分析全部13个文件
fprintf('将分析全部 %d 个文件\n', test_files);

%% -------- 收集数据 --------
all_depol_data = [];
all_backscatter_data = [];
all_labels = [];
file_info = [];

for fileIdx = 1:test_files
    try
        % 获取文件信息
        depolFile = depolFiles(fileIdx).name;
        fprintf('\n=== 分析文件 %d/%d: %s ===\n', fileIdx, test_files, depolFile);
        
        % 从文件名提取日期
        dateMatch = regexp(depolFile, 'Depol_(\d{8})', 'tokens');
        if isempty(dateMatch)
            fprintf('  警告: 无法从文件名解析日期，跳过\n');
            continue;
        end
        dateStr = dateMatch{1}{1};
        dateFormatted = [dateStr(1:4), '-', dateStr(5:6), '-', dateStr(7:8)];
        
        % 查找对应文件
        backscatterFile = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', dateStr));
        backscatterFiles = dir(backscatterFile);
        if isempty(backscatterFiles)
            fprintf('  警告: 找不到对应的BackScatter文件，跳过\n');
            continue;
        end
        
        era5File = fullfile(era5Dir, sprintf('era5_temp_geopotential_%s.nc', dateStr));
        if ~exist(era5File, 'file')
            fprintf('  警告: 找不到对应的ERA5文件，跳过\n');
            continue;
        end
        
        % 加载数据
        [lidar_data, success] = load_lidar_data_clustering(lidarDir, backscatterDir, depolFile, backscatterFiles(1).name);
        if ~success
            fprintf('  错误: 激光雷达数据加载失败\n');
            continue;
        end
        
        [era5_data, success] = load_era5_data_clustering(era5File);
        if ~success
            fprintf('  错误: ERA5数据加载失败\n');
            continue;
        end
        
        % 简化的云识别
        [cloud_mask, ~] = identify_clouds_clustering(lidar_data);
        fprintf('  云识别完成，云覆盖率: %.1f%%\n', sum(cloud_mask(:))/numel(cloud_mask)*100);
        
        % 温度约束
        [temp_mask, height_info] = apply_temperature_constraint_clustering(lidar_data, era5_data, temp_min, temp_max);
        fprintf('  温度约束完成，过冷区域覆盖率: %.1f%%\n', sum(temp_mask(:))/numel(temp_mask)*100);
        
        % 综合约束条件：云层 + 过冷温度
        base_mask = cloud_mask & temp_mask;
        fprintf('  综合约束后覆盖率: %.1f%%\n', sum(base_mask(:))/numel(base_mask)*100);
        
        % 提取过冷水区域的数据
        [depol_samples, backscatter_samples] = extract_supercooled_samples(lidar_data, base_mask);
        
        if ~isempty(depol_samples)
            all_depol_data = [all_depol_data; depol_samples];
            all_backscatter_data = [all_backscatter_data; backscatter_samples];
            all_labels = [all_labels; ones(length(depol_samples), 1) * fileIdx];
            
            file_info = [file_info; struct('date', dateFormatted, 'samples', length(depol_samples), ...
                                          'height_info', height_info)];
            
            fprintf('  提取样本数: %d\n', length(depol_samples));
        end
        
        fprintf('  处理完成: %s\n', dateFormatted);
        
    catch ME
        fprintf('  处理文件 %s 时出错: %s\n', depolFile, ME.message);
        continue;
    end
end

%% -------- 聚类分析 --------
if ~isempty(all_depol_data)
    fprintf('\n开始聚类分析...\n');
    fprintf('总样本数: %d\n', length(all_depol_data));
    
    % 执行聚类分析
    clustering_results = perform_clustering_analysis(all_depol_data, all_backscatter_data, all_labels);
    
    % 生成分析图表
    generate_clustering_plots(all_depol_data, all_backscatter_data, all_labels, clustering_results, file_info, outputDir);
    
    % 生成阈值建议报告
    generate_threshold_report(clustering_results, file_info, outputDir);
    
    fprintf('\n聚类分析完成！\n');
    fprintf('结果保存在: %s\n', outputDir);
else
    fprintf('\n没有收集到有效的样本数据\n');
end

end

%% -------- 子函数：加载激光雷达数据 --------
function [lidar_data, success] = load_lidar_data_clustering(lidarDir, backscatterDir, depolFile, backscatterFile)
    success = false;
    lidar_data = struct();
    
    try
        % 加载数据
        depolPath = fullfile(lidarDir, depolFile);
        raw_depol = readmatrix(depolPath);
        
        backscatterPath = fullfile(backscatterDir, backscatterFile);
        raw_backscatter = readmatrix(backscatterPath);
        
        % 处理高度数据
        height = raw_depol(:,1);
        valid_idx = ~isnan(height);
        height = height(valid_idx);
        
        % 限制高度范围到4km以内
        height_mask = height <= 4000;
        height = height(height_mask);
        
        % 获取数据（限制时间点数以提高处理速度）
        max_time_points = 480;  % 8小时的数据用于聚类分析
        depol_data = raw_depol(valid_idx, 2:min(end, max_time_points+1));
        depol_data = depol_data(height_mask, :);
        
        backscatter_data = raw_backscatter(valid_idx, 2:min(end, max_time_points+1));
        backscatter_data = backscatter_data(height_mask, :);
        
        % 创建时间向量
        num_times = size(depol_data, 2);
        time_vector = 0:(num_times-1);
        
        % 存储数据
        lidar_data.height = height;
        lidar_data.time = time_vector;
        lidar_data.depol = double(depol_data);
        lidar_data.backscatter = double(backscatter_data);
        
        fprintf('    激光雷达数据加载成功 (高度: %.0f-%.0f m, 时间: %d 分钟)\n', ...
                min(height), max(height), num_times);
        
        success = true;
        
    catch ME
        fprintf('    激光雷达数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：加载ERA5数据 --------
function [era5_data, success] = load_era5_data_clustering(era5File)
    success = false;
    era5_data = struct();
    
    try
        % 读取ERA5数据
        temperature = ncread(era5File, 't');
        geopotential = ncread(era5File, 'z');
        pressure_levels = ncread(era5File, 'pressure_level');
        time = ncread(era5File, 'valid_time');
        
        % 转换单位
        temperature_celsius = squeeze(temperature) - 273.15;
        geopotential_height = squeeze(geopotential) / 9.80665;
        
        % 存储数据
        era5_data.temperature = temperature_celsius;
        era5_data.height = geopotential_height;
        era5_data.pressure = pressure_levels;
        era5_data.time = time;
        
        fprintf('    ERA5数据加载成功\n');
        success = true;
        
    catch ME
        fprintf('    ERA5数据加载失败: %s\n', ME.message);
    end
end

%% -------- 子函数：简化的云识别 --------
function [cloud_mask, cloud_stats] = identify_clouds_clustering(lidar_data)
    % 简化版本，使用后向散射阈值识别云层
    fprintf('    应用简化云识别算法...\n');
    
    % 使用后向散射阈值来识别云层
    cloud_threshold = 1e-5;  % 后向散射阈值
    cloud_mask = lidar_data.backscatter > cloud_threshold;
    
    % 统计信息
    cloud_stats = struct();
    cloud_stats.threshold_detections = sum(cloud_mask(:));
    cloud_stats.water_cloud_detections = 0;
    cloud_stats.gradient_detections = 0;
end

%% -------- 子函数：温度约束 --------
function [temp_mask, height_info] = apply_temperature_constraint_clustering(lidar_data, era5_data, temp_min, temp_max)
    fprintf('    应用过冷水温度约束...\n');

    % 初始化温度掩码
    temp_mask = false(size(lidar_data.depol));

    % 计算平均温度剖面
    mean_temp_profile = mean(era5_data.temperature, 2);
    mean_height_profile = mean(era5_data.height, 2);

    % 插值到激光雷达高度网格
    temp_interp_mean = interp1(mean_height_profile, mean_temp_profile, lidar_data.height, 'linear', 'extrap');

    % 找到过冷水温度范围对应的高度范围
    supercooled_indices = find((temp_interp_mean >= temp_min) & (temp_interp_mean <= temp_max));

    if ~isempty(supercooled_indices)
        supercooled_height_min = lidar_data.height(min(supercooled_indices));
        supercooled_height_max = lidar_data.height(max(supercooled_indices));

        % 找到0°C和-40°C对应的具体高度
        [~, zero_idx] = min(abs(temp_interp_mean - 0));
        [~, minus40_idx] = min(abs(temp_interp_mean - (-40)));

        zero_height = lidar_data.height(zero_idx);
        minus40_height = lidar_data.height(minus40_idx);

        height_info = struct();
        height_info.supercooled_range = [supercooled_height_min, supercooled_height_max];
        height_info.zero_height = zero_height;
        height_info.minus40_height = minus40_height;

        fprintf('    过冷水高度范围: %.0f - %.0f m\n', supercooled_height_min, supercooled_height_max);
    else
        height_info = struct();
        height_info.supercooled_range = [NaN, NaN];
        height_info.zero_height = NaN;
        height_info.minus40_height = NaN;

        fprintf('    警告: 未找到过冷水温度区域\n');
    end

    % 对每个时间点应用温度约束
    for t = 1:length(lidar_data.time)
        era5_time_idx = min(t, size(era5_data.temperature, 2));
        temp_profile = era5_data.temperature(:, era5_time_idx);
        height_profile = era5_data.height(:, era5_time_idx);
        temp_interp = interp1(height_profile, temp_profile, lidar_data.height, 'linear', 'extrap');
        temp_condition = (temp_interp >= temp_min) & (temp_interp <= temp_max);
        temp_mask(:, t) = temp_condition;
    end
end

%% -------- 子函数：提取过冷水样本 --------
function [depol_samples, backscatter_samples] = extract_supercooled_samples(lidar_data, base_mask)
    % 从过冷水区域提取Depol和BackScatter样本

    % 获取有效的样本点
    valid_indices = base_mask & isfinite(lidar_data.depol) & isfinite(lidar_data.backscatter) & ...
                    (lidar_data.depol > 0) & (lidar_data.backscatter > 0);

    % 提取样本
    depol_samples = lidar_data.depol(valid_indices);
    backscatter_samples = lidar_data.backscatter(valid_indices);

    % 数据预处理：移除极端值
    depol_p99 = prctile(depol_samples, 99);
    backscatter_p99 = prctile(backscatter_samples, 99);

    valid_samples = (depol_samples <= depol_p99) & (backscatter_samples <= backscatter_p99);
    depol_samples = depol_samples(valid_samples);
    backscatter_samples = backscatter_samples(valid_samples);

    % 随机采样以减少计算量（每个文件最多5000个样本）
    if length(depol_samples) > 5000
        sample_indices = randperm(length(depol_samples), 5000);
        depol_samples = depol_samples(sample_indices);
        backscatter_samples = backscatter_samples(sample_indices);
    end
end

%% -------- 子函数：执行聚类分析 --------
function clustering_results = perform_clustering_analysis(depol_data, backscatter_data, labels)
    fprintf('执行聚类分析...\n');

    % 数据预处理：对数变换和标准化
    log_depol = log10(depol_data + 1e-6);
    log_backscatter = log10(backscatter_data + 1e-6);

    % 标准化数据
    data_matrix = [log_depol, log_backscatter];
    data_normalized = (data_matrix - mean(data_matrix)) ./ std(data_matrix);

    % 简化的K-means聚类 (k=2: 过冷水 vs 其他)
    fprintf('  执行简化K-means聚类 (k=2)...\n');
    [kmeans_idx, kmeans_centers] = simple_kmeans(data_normalized, 2);

    % 简化的K-means聚类 (k=3: 更细分的分类)
    fprintf('  执行简化K-means聚类 (k=3)...\n');
    [kmeans3_idx, kmeans3_centers] = simple_kmeans(data_normalized, 3);

    % 使用K-means结果作为GMM的替代
    fprintf('  使用K-means结果...\n');
    gmm_idx = kmeans_idx;
    gmm_model = [];

    % 分析聚类结果，确定过冷水聚类
    [supercooled_cluster, threshold_suggestions] = analyze_clusters(depol_data, backscatter_data, kmeans_idx, kmeans3_idx);

    % 存储结果
    clustering_results = struct();
    clustering_results.original_data = [depol_data, backscatter_data];
    clustering_results.normalized_data = data_normalized;
    clustering_results.log_data = [log_depol, log_backscatter];
    clustering_results.kmeans2_idx = kmeans_idx;
    clustering_results.kmeans2_centers = kmeans_centers;
    clustering_results.kmeans3_idx = kmeans3_idx;
    clustering_results.kmeans3_centers = kmeans3_centers;
    clustering_results.gmm_idx = gmm_idx;
    clustering_results.gmm_model = gmm_model;
    clustering_results.supercooled_cluster = supercooled_cluster;
    clustering_results.threshold_suggestions = threshold_suggestions;
    clustering_results.labels = labels;

    fprintf('聚类分析完成\n');
end

%% -------- 子函数：分析聚类结果 --------
function [supercooled_cluster, threshold_suggestions] = analyze_clusters(depol_data, backscatter_data, kmeans2_idx, kmeans3_idx)
    fprintf('  分析聚类结果...\n');

    % 分析K-means (k=2)结果
    cluster1_depol = depol_data(kmeans2_idx == 1);
    cluster1_backscatter = backscatter_data(kmeans2_idx == 1);
    cluster2_depol = depol_data(kmeans2_idx == 2);
    cluster2_backscatter = backscatter_data(kmeans2_idx == 2);

    % 计算每个聚类的统计特征
    cluster1_stats = struct();
    cluster1_stats.depol_mean = mean(cluster1_depol);
    cluster1_stats.depol_std = std(cluster1_depol);
    cluster1_stats.backscatter_mean = mean(cluster1_backscatter);
    cluster1_stats.backscatter_std = std(cluster1_backscatter);
    cluster1_stats.size = length(cluster1_depol);

    cluster2_stats = struct();
    cluster2_stats.depol_mean = mean(cluster2_depol);
    cluster2_stats.depol_std = std(cluster2_depol);
    cluster2_stats.backscatter_mean = mean(cluster2_backscatter);
    cluster2_stats.backscatter_std = std(cluster2_backscatter);
    cluster2_stats.size = length(cluster2_depol);

    % 确定哪个聚类更可能是过冷水（低Depol，适中BackScatter）
    if cluster1_stats.depol_mean < cluster2_stats.depol_mean
        supercooled_cluster = 1;
        supercooled_stats = cluster1_stats;
    else
        supercooled_cluster = 2;
        supercooled_stats = cluster2_stats;
    end

    % 基于聚类结果计算阈值建议
    if supercooled_cluster == 1
        supercooled_depol_data = cluster1_depol;
        supercooled_backscatter_data = cluster1_backscatter;
    else
        supercooled_depol_data = cluster2_depol;
        supercooled_backscatter_data = cluster2_backscatter;
    end

    % 计算阈值建议
    threshold_suggestions = struct();

    % 方法1: 基于分位数
    threshold_suggestions.depol_p75 = prctile(supercooled_depol_data, 75);
    threshold_suggestions.depol_p90 = prctile(supercooled_depol_data, 90);
    threshold_suggestions.depol_p95 = prctile(supercooled_depol_data, 95);

    threshold_suggestions.backscatter_p25 = prctile(supercooled_backscatter_data, 25);
    threshold_suggestions.backscatter_p10 = prctile(supercooled_backscatter_data, 10);
    threshold_suggestions.backscatter_p5 = prctile(supercooled_backscatter_data, 5);

    % 方法2: 基于均值和标准差
    threshold_suggestions.depol_mean_plus_std = supercooled_stats.depol_mean + supercooled_stats.depol_std;
    threshold_suggestions.backscatter_mean_minus_std = max(0.001, supercooled_stats.backscatter_mean - supercooled_stats.backscatter_std);

    % 方法3: 基于目标值的最接近值
    target_depol = 0.1;
    target_backscatter = 0.04;

    [~, closest_depol_idx] = min(abs(supercooled_depol_data - target_depol));
    [~, closest_backscatter_idx] = min(abs(supercooled_backscatter_data - target_backscatter));

    threshold_suggestions.closest_to_target_depol = supercooled_depol_data(closest_depol_idx);
    threshold_suggestions.closest_to_target_backscatter = supercooled_backscatter_data(closest_backscatter_idx);

    fprintf('    过冷水聚类: %d (样本数: %d)\n', supercooled_cluster, supercooled_stats.size);
    fprintf('    Depol统计: 均值=%.3f, 标准差=%.3f\n', supercooled_stats.depol_mean, supercooled_stats.depol_std);
    fprintf('    BackScatter统计: 均值=%.4f, 标准差=%.4f\n', supercooled_stats.backscatter_mean, supercooled_stats.backscatter_std);
end

%% -------- 子函数：生成聚类分析图表 --------
function generate_clustering_plots(depol_data, backscatter_data, labels, clustering_results, file_info, outputDir)
    fprintf('生成聚类分析图表...\n');

    % 创建大图
    fig = figure('Position', [100, 100, 1600, 1200]);

    % 子图1: 原始数据散点图
    subplot(2, 3, 1);
    scatter(depol_data, backscatter_data, 20, labels, 'filled');
    xlabel('Depol');
    ylabel('BackScatter');
    title('原始数据分布');
    colorbar;
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 子图2: K-means (k=2) 聚类结果
    subplot(2, 3, 2);
    scatter(depol_data, backscatter_data, 20, clustering_results.kmeans2_idx, 'filled');
    xlabel('Depol');
    ylabel('BackScatter');
    title('K-means聚类 (k=2)');
    colormap(gca, [0.8 0.2 0.2; 0.2 0.2 0.8]);
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 添加聚类中心
    hold on;
    centers_original = exp(clustering_results.kmeans2_centers);  % 转换回原始尺度
    plot(centers_original(:,1), centers_original(:,2), 'kx', 'MarkerSize', 15, 'LineWidth', 3);
    hold off;

    % 子图3: K-means (k=3) 聚类结果
    subplot(2, 3, 3);
    scatter(depol_data, backscatter_data, 20, clustering_results.kmeans3_idx, 'filled');
    xlabel('Depol');
    ylabel('BackScatter');
    title('K-means聚类 (k=3)');
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 子图4: 过冷水聚类突出显示
    subplot(2, 3, 4);
    supercooled_mask = clustering_results.kmeans2_idx == clustering_results.supercooled_cluster;
    scatter(depol_data(~supercooled_mask), backscatter_data(~supercooled_mask), 20, [0.7 0.7 0.7], 'filled');
    hold on;
    scatter(depol_data(supercooled_mask), backscatter_data(supercooled_mask), 20, [0.2 0.6 0.9], 'filled');
    xlabel('Depol');
    ylabel('BackScatter');
    title('过冷水聚类识别');
    legend('其他', '过冷水', 'Location', 'best');
    grid on;
    set(gca, 'XScale', 'log', 'YScale', 'log');

    % 添加阈值线
    ts = clustering_results.threshold_suggestions;
    xline(ts.depol_p90, 'r--', 'LineWidth', 2, 'Label', sprintf('Depol P90=%.3f', ts.depol_p90));
    yline(ts.backscatter_p10, 'r--', 'LineWidth', 2, 'Label', sprintf('BackScatter P10=%.4f', ts.backscatter_p10));
    hold off;

    % 子图5: Depol分布直方图
    subplot(2, 3, 5);
    histogram(depol_data(supercooled_mask), 50, 'FaceColor', [0.2 0.6 0.9], 'EdgeColor', 'none');
    hold on;
    histogram(depol_data(~supercooled_mask), 50, 'FaceColor', [0.7 0.7 0.7], 'EdgeColor', 'none');
    xlabel('Depol');
    ylabel('频数');
    title('Depol分布对比');
    legend('过冷水', '其他', 'Location', 'best');
    set(gca, 'XScale', 'log');
    grid on;

    % 添加阈值线
    xline(0.1, 'k--', 'LineWidth', 2, 'Label', '目标阈值 0.1');
    xline(ts.depol_p90, 'r--', 'LineWidth', 2, 'Label', sprintf('建议阈值 %.3f', ts.depol_p90));
    hold off;

    % 子图6: BackScatter分布直方图
    subplot(2, 3, 6);
    histogram(backscatter_data(supercooled_mask), 50, 'FaceColor', [0.2 0.6 0.9], 'EdgeColor', 'none');
    hold on;
    histogram(backscatter_data(~supercooled_mask), 50, 'FaceColor', [0.7 0.7 0.7], 'EdgeColor', 'none');
    xlabel('BackScatter');
    ylabel('频数');
    title('BackScatter分布对比');
    legend('过冷水', '其他', 'Location', 'best');
    set(gca, 'XScale', 'log');
    grid on;

    % 添加阈值线
    xline(0.04, 'k--', 'LineWidth', 2, 'Label', '目标阈值 0.04');
    xline(ts.backscatter_p10, 'r--', 'LineWidth', 2, 'Label', sprintf('建议阈值 %.4f', ts.backscatter_p10));
    hold off;

    % 添加总标题
    sgtitle('过冷水识别聚类分析结果', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, 'clustering_analysis_results.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存聚类分析图: %s\n', outputFile);

    close(fig);

    % 生成阈值对比图
    generate_threshold_comparison_plot(clustering_results, outputDir);
end

%% -------- 子函数：生成阈值对比图 --------
function generate_threshold_comparison_plot(clustering_results, outputDir)
    fig = figure('Position', [100, 100, 1200, 800]);

    ts = clustering_results.threshold_suggestions;

    % 子图1: Depol阈值对比
    subplot(1, 2, 1);
    methods = {'P75', 'P90', 'P95', 'Mean+Std', '最接近0.1'};
    depol_values = [ts.depol_p75, ts.depol_p90, ts.depol_p95, ts.depol_mean_plus_std, ts.closest_to_target_depol];

    bar(depol_values, 'FaceColor', [0.3 0.6 0.9]);
    set(gca, 'XTickLabel', methods);
    ylabel('Depol阈值');
    title('Depol阈值建议对比');
    grid on;

    % 添加目标线
    hold on;
    yline(0.1, 'r--', 'LineWidth', 2, 'Label', '目标值 0.1');
    hold off;

    % 添加数值标签
    for i = 1:length(depol_values)
        text(i, depol_values(i) + max(depol_values)*0.02, sprintf('%.3f', depol_values(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    % 子图2: BackScatter阈值对比
    subplot(1, 2, 2);
    backscatter_methods = {'P25', 'P10', 'P5', 'Mean-Std', '最接近0.04'};
    backscatter_values = [ts.backscatter_p25, ts.backscatter_p10, ts.backscatter_p5, ...
                         ts.backscatter_mean_minus_std, ts.closest_to_target_backscatter];

    bar(backscatter_values, 'FaceColor', [0.9 0.6 0.3]);
    set(gca, 'XTickLabel', backscatter_methods);
    ylabel('BackScatter阈值');
    title('BackScatter阈值建议对比');
    grid on;

    % 添加目标线
    hold on;
    yline(0.04, 'r--', 'LineWidth', 2, 'Label', '目标值 0.04');
    hold off;

    % 添加数值标签
    for i = 1:length(backscatter_values)
        text(i, backscatter_values(i) + max(backscatter_values)*0.02, sprintf('%.4f', backscatter_values(i)), ...
             'HorizontalAlignment', 'center', 'FontWeight', 'bold');
    end

    sgtitle('聚类分析阈值建议', 'FontSize', 16, 'FontWeight', 'bold');

    % 保存图像
    outputFile = fullfile(outputDir, 'threshold_comparison.png');
    print(fig, outputFile, '-dpng', '-r300');
    fprintf('保存阈值对比图: %s\n', outputFile);

    close(fig);
end

%% -------- 子函数：生成阈值建议报告 --------
function generate_threshold_report(clustering_results, file_info, outputDir)
    fprintf('生成阈值建议报告...\n');

    % 创建报告文件
    reportFile = fullfile(outputDir, 'clustering_threshold_report.txt');

    fid = fopen(reportFile, 'w');
    if fid == -1
        fprintf('错误: 无法创建报告文件\n');
        return;
    end

    try
        ts = clustering_results.threshold_suggestions;

        % 写入报告头部
        fprintf(fid, '========================================\n');
        fprintf(fid, '聚类分析阈值建议报告\n');
        fprintf(fid, '========================================\n');
        fprintf(fid, '生成时间: %s\n', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
        fprintf(fid, '分析目的: 基于聚类方法确定过冷水识别的最优阈值\n\n');

        % 数据基本信息
        fprintf(fid, '1. 数据基本信息\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '分析文件数: %d\n', length(file_info));
        fprintf(fid, '总样本数: %d\n', size(clustering_results.original_data, 1));

        for i = 1:length(file_info)
            fprintf(fid, '  %s: %d 样本\n', file_info(i).date, file_info(i).samples);
        end
        fprintf(fid, '\n');

        % 聚类结果
        fprintf(fid, '2. 聚类分析结果\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '过冷水聚类编号: %d\n', clustering_results.supercooled_cluster);

        supercooled_mask = clustering_results.kmeans2_idx == clustering_results.supercooled_cluster;
        supercooled_data = clustering_results.original_data(supercooled_mask, :);
        other_data = clustering_results.original_data(~supercooled_mask, :);

        fprintf(fid, '过冷水聚类样本数: %d (%.1f%%)\n', sum(supercooled_mask), sum(supercooled_mask)/length(supercooled_mask)*100);
        fprintf(fid, '其他聚类样本数: %d (%.1f%%)\n', sum(~supercooled_mask), sum(~supercooled_mask)/length(supercooled_mask)*100);
        fprintf(fid, '\n');

        % 过冷水聚类统计
        fprintf(fid, '过冷水聚类统计特征:\n');
        fprintf(fid, '  Depol: 均值=%.4f, 标准差=%.4f, 中位数=%.4f\n', ...
                mean(supercooled_data(:,1)), std(supercooled_data(:,1)), median(supercooled_data(:,1)));
        fprintf(fid, '  BackScatter: 均值=%.6f, 标准差=%.6f, 中位数=%.6f\n', ...
                mean(supercooled_data(:,2)), std(supercooled_data(:,2)), median(supercooled_data(:,2)));
        fprintf(fid, '\n');

        % 其他聚类统计
        fprintf(fid, '其他聚类统计特征:\n');
        fprintf(fid, '  Depol: 均值=%.4f, 标准差=%.4f, 中位数=%.4f\n', ...
                mean(other_data(:,1)), std(other_data(:,1)), median(other_data(:,1)));
        fprintf(fid, '  BackScatter: 均值=%.6f, 标准差=%.6f, 中位数=%.6f\n', ...
                mean(other_data(:,2)), std(other_data(:,2)), median(other_data(:,2)));
        fprintf(fid, '\n');

        % 阈值建议
        fprintf(fid, '3. 阈值建议\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, 'Depol阈值建议:\n');
        fprintf(fid, '  75分位数: %.4f\n', ts.depol_p75);
        fprintf(fid, '  90分位数: %.4f\n', ts.depol_p90);
        fprintf(fid, '  95分位数: %.4f\n', ts.depol_p95);
        fprintf(fid, '  均值+标准差: %.4f\n', ts.depol_mean_plus_std);
        fprintf(fid, '  最接近目标0.1: %.4f\n', ts.closest_to_target_depol);
        fprintf(fid, '\n');

        fprintf(fid, 'BackScatter阈值建议:\n');
        fprintf(fid, '  25分位数: %.6f\n', ts.backscatter_p25);
        fprintf(fid, '  10分位数: %.6f\n', ts.backscatter_p10);
        fprintf(fid, '  5分位数: %.6f\n', ts.backscatter_p5);
        fprintf(fid, '  均值-标准差: %.6f\n', ts.backscatter_mean_minus_std);
        fprintf(fid, '  最接近目标0.04: %.6f\n', ts.closest_to_target_backscatter);
        fprintf(fid, '\n');

        % 推荐阈值
        fprintf(fid, '4. 推荐阈值组合\n');
        fprintf(fid, '----------------------------------------\n');

        % 计算与目标值的接近程度
        depol_target_diff = abs([ts.depol_p75, ts.depol_p90, ts.depol_p95, ts.depol_mean_plus_std, ts.closest_to_target_depol] - 0.1);
        backscatter_target_diff = abs([ts.backscatter_p25, ts.backscatter_p10, ts.backscatter_p5, ts.backscatter_mean_minus_std, ts.closest_to_target_backscatter] - 0.04);

        [~, best_depol_idx] = min(depol_target_diff);
        [~, best_backscatter_idx] = min(backscatter_target_diff);

        depol_methods = {'P75', 'P90', 'P95', 'Mean+Std', 'Closest'};
        backscatter_methods = {'P25', 'P10', 'P5', 'Mean-Std', 'Closest'};
        depol_values = [ts.depol_p75, ts.depol_p90, ts.depol_p95, ts.depol_mean_plus_std, ts.closest_to_target_depol];
        backscatter_values = [ts.backscatter_p25, ts.backscatter_p10, ts.backscatter_p5, ts.backscatter_mean_minus_std, ts.closest_to_target_backscatter];

        fprintf(fid, '最接近目标的Depol阈值: %.4f (%s方法)\n', depol_values(best_depol_idx), depol_methods{best_depol_idx});
        fprintf(fid, '最接近目标的BackScatter阈值: %.6f (%s方法)\n', backscatter_values(best_backscatter_idx), backscatter_methods{best_backscatter_idx});
        fprintf(fid, '\n');

        fprintf(fid, '推荐参数组合:\n');
        fprintf(fid, '  Depol < %.4f\n', depol_values(best_depol_idx));
        fprintf(fid, '  BackScatter > %.6f\n', backscatter_values(best_backscatter_idx));
        fprintf(fid, '\n');

        % 与目标值对比
        fprintf(fid, '5. 与目标值对比\n');
        fprintf(fid, '----------------------------------------\n');
        fprintf(fid, '目标值: Depol < 0.1, BackScatter > 0.04\n');
        fprintf(fid, '聚类建议: Depol < %.4f, BackScatter > %.6f\n', depol_values(best_depol_idx), backscatter_values(best_backscatter_idx));
        fprintf(fid, 'Depol差异: %.4f (%.1f%%)\n', depol_values(best_depol_idx) - 0.1, (depol_values(best_depol_idx) - 0.1)/0.1*100);
        fprintf(fid, 'BackScatter差异: %.6f (%.1f%%)\n', backscatter_values(best_backscatter_idx) - 0.04, (backscatter_values(best_backscatter_idx) - 0.04)/0.04*100);
        fprintf(fid, '\n');

        % 结论
        fprintf(fid, '6. 结论\n');
        fprintf(fid, '----------------------------------------\n');
        if abs(depol_values(best_depol_idx) - 0.1) < 0.02 && abs(backscatter_values(best_backscatter_idx) - 0.04) < 0.01
            fprintf(fid, '聚类分析结果与目标阈值高度一致，验证了阈值选择的合理性。\n');
        elseif abs(depol_values(best_depol_idx) - 0.1) < 0.05 && abs(backscatter_values(best_backscatter_idx) - 0.04) < 0.02
            fprintf(fid, '聚类分析结果与目标阈值基本一致，目标阈值是合理的选择。\n');
        else
            fprintf(fid, '聚类分析建议的阈值与目标值存在一定差异，建议考虑调整阈值。\n');
        end

        fprintf(fid, '基于聚类分析，过冷水识别的数据特征已被成功识别和量化。\n');
        fprintf(fid, '建议在实际应用中结合物理意义和识别效果进行最终阈值确定。\n');
        fprintf(fid, '\n');

        fprintf(fid, '========================================\n');
        fprintf(fid, '报告结束\n');
        fprintf(fid, '========================================\n');

        fclose(fid);
        fprintf('保存阈值建议报告: %s\n', reportFile);

    catch ME
        fclose(fid);
        fprintf('生成报告时出错: %s\n', ME.message);
    end
end

%% -------- 子函数：简化的K-means聚类 --------
function [idx, centers] = simple_kmeans(data, k)
    % 简化的K-means聚类实现，不依赖Statistics Toolbox

    [n, d] = size(data);

    % 随机初始化聚类中心
    rng(42); % 设置随机种子以获得可重复结果
    centers = data(randperm(n, k), :);

    max_iter = 100;
    tolerance = 1e-6;

    for iter = 1:max_iter
        % 分配每个点到最近的聚类中心
        distances = zeros(n, k);
        for i = 1:k
            diff = data - repmat(centers(i, :), n, 1);
            distances(:, i) = sum(diff.^2, 2);
        end

        [~, idx] = min(distances, [], 2);

        % 更新聚类中心
        new_centers = zeros(k, d);
        for i = 1:k
            cluster_points = data(idx == i, :);
            if ~isempty(cluster_points)
                new_centers(i, :) = mean(cluster_points, 1);
            else
                new_centers(i, :) = centers(i, :); % 保持原中心
            end
        end

        % 检查收敛
        if max(max(abs(new_centers - centers))) < tolerance
            break;
        end

        centers = new_centers;
    end

    fprintf('    K-means收敛于第%d次迭代\n', iter);
end
