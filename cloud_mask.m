function [z, cloud, F_threshold, F2_threshold, maxHeight] = cloud_mask(signal)
% CLOUD_MASK 检测云和滤除气溶胶
%   这个函数基于Python的cloud_mask函数转换而来，用于从激光雷达信号中检测云层
%   并滤除气溶胶。
%
% 输入参数:
%   signal - 包含激光雷达数据的结构体，必须包含以下字段:
%     - height: 高度数据 (单位: km)
%     - nrb: 标准化的后向散射数据 [时间,高度]
%     - time: 时间数据
%
% 输出参数:
%   z - 用于云检测的高度向量 (单位: km)
%   cloud - 云标记矩阵 [时间, 高度]，其中：
%          0 = 非云或无数据
%          1 = 云
%   F_threshold - 使用的F阈值
%   F2_threshold - 使用的F2阈值
%   maxHeight - 最大检测高度 (单位: km)

    % 设置阈值参数
F_threshold = 500;
F2_threshold = 4;

% 定义盲区
blind = 1;  % 修改为1以包含第一个点

% 检查输入参数
if nargin < 1
    error('输入参数的数目不足。需要提供信号结构体。');
end

% 获取数据维度
Ntime = size(signal.nrb, 1);  % 时间点数
Nheight = length(signal.height);  % 高度点数

% 提取高度、后向散射和时间数据
height = signal.height(blind:min(1800, Nheight));
if length(height) < 3
    cloud = zeros(Ntime, 1);
    return;
end

nrb = signal.nrb(:, blind:min(1800, Nheight));
time = signal.time;

    % 获取数据维度
Nbin = length(height);
Ntime = size(nrb, 1);  % 使用nrb的第一维作为时间维度

% 设置最大检测高度
maxHeight = 15.0; % km

% 截取高度小于等于maxHeight的部分
z_idx = height <= maxHeight;
if sum(z_idx) == 0
    % 如果没有点满足条件，使用所有点
    z_idx = ones(size(height), 'logical');
end
z = height(z_idx);
Nz = length(z);

% 检查是否有足够的高度点
if Nz < 3
    cloud = zeros(Ntime, 1);
    return;
end

% 初始化云标记矩阵
cloud = zeros(Ntime, Nz);

% 遍历每个时间点
for itime = 1:Ntime
    % 确保索引在有效范围内
    nrb_cols = size(nrb, 2);
    max_height_idx = find(height == maxHeight, 1);
    if isempty(max_height_idx) || max_height_idx > nrb_cols
        [~, max_height_idx] = min(abs(height - maxHeight));
        % 确保索引不超过范围
        max_height_idx = min(max_height_idx, nrb_cols);
    end
    
    if max_height_idx <= nrb_cols && ~isnan(nrb(itime, max_height_idx))
        % 计算未经距离校正的信号
        height_section = height(1:min(nrb_cols, length(height)));
        P = nrb(itime, 1:min(nrb_cols, length(height))) ./ (height_section.^2);
        PM = nrb(itime, 1:min(nrb_cols, length(height)));
        
        % 计算噪声水平
        Pnoise_idx = height_section >= maxHeight;
        if any(Pnoise_idx) && sum(Pnoise_idx) < length(P)
            Pnoise = P(Pnoise_idx);
            sd = nanstd(Pnoise);
        else
            % 如果无法计算噪声，使用默认值
            sd = 0.01;
        end
    end
end
    
    % 计算未经距离校正的信号
    P = nrb(itime, 1:min(end, length(height))) ./ (height(1:min(end, length(height))).^2);
    PM = nrb(itime, 1:min(end, length(height)));
    
    % 计算噪声水平
    height_section = height(1:min(end, length(height)));
    Pnoise_idx = height_section >= maxHeight;
    if any(Pnoise_idx) && sum(Pnoise_idx) < length(P)
        Pnoise = P(Pnoise_idx);
        sd = nanstd(Pnoise);
    else
        % 如果无法计算噪声，使用默认值
        sd = 0.01;
    end
    
    % 检查SD是否为NaN
    if isnan(sd)
        cloud(itime, :) = NaN;
        return;  % 替换continue为return，因为continue只能在循环中使用
    end

    % 设置噪声阈值
    k = 6;
    noise = k * sd;
    
    % 去除噪声，获取高度小于等于maxHeight的信号
    P = P(height <= maxHeight);
    
    % 平滑信号 (使用移动平均替代Python的moving_average)
    Ps = movmean(P, 3);
    
    % 初始化前向和后向处理的数据
    PD1 = Ps;
    PD2 = Ps;
    
    % 前向扫描处理
    for zi = 1:(Nz-1)
        if ~(abs(PD1(zi+1) - PD1(zi)) >= noise)
            PD1(zi+1) = PD1(zi);
        end
    end
    
    % 后向扫描处理
    for zi = 1:(Nz-1)
        if ~(abs(PD2(Nz-zi) - PD2(Nz-zi+1)) >= noise)
            PD2(Nz-zi) = PD2(Nz-zi+1);
        end
    end
    
    % 检查PD1和PD2是否有效
    if all(isnan(PD1)) || all(isnan(PD2))
        cloud(itime, :) = NaN;
        return;  % 替换continue为return，因为continue只能在循环中使用
    end
    
    % 计算平均值
    PD = (PD1 + PD2) / 2;
    
    % 按升序排列
    [Rs, Is] = sort(PD);
    MA = nanmax(Rs);
    MI = nanmin(Rs);
    PE = (1:Nz) / Nz;
    
    % 处理相等值
    for i = 1:(Nz-1)
        if Rs(i+1) == Rs(i)
            PE(i+1) = PE(i);
        end
    end
    
    % 计算y值
    y = PE .* (MA - MI) + MI;
    
    % 初始化PN
    PN = zeros(1, Nz);
    PN(Is) = y;
    
    % 计算基线
    B = ((Nz:-1:1) / Nz) .* (MA - MI) + MI;
    
    % 初始化存储数组
    base = [];
    top = [];
    Area = [];
    
    % 检测层边界
    for zi = 1:(Nz-1)
        if ~(PN(zi+1) > B(zi+1) && PN(zi) <= B(zi))
            continue;  % 如果没有检测到边界，跳过此zi点
        end
        
        s = 0;
        if PN(zi+1) > PN(zi)
            for i = 1:(Nz-zi-2)
                if zi+i+2 <= Nz
                    if PN(zi+i+2) > B(zi+i+2)
                        s = s + PN(zi+i+2) - B(zi+i+2);
                    end
                    
                    if PN(zi+i+2) <= B(zi+i+2)
                        if i+1 >= 3
                            base = [base, zi+1];
                            top = [top, zi+i+2];
                            if s <= (B(zi+1) - B(zi+i+2)) * (i+1) / 2
                                s = 0;
                            end
                            Area = [Area, s];
                        end
                        break;
                    end
                else
                    break;
                end
            end
        end
    end
    
    % 获取层数
    Nlayer = length(base);
    
    % 计算对数NRB
    if any(PN(:))  % 只有当PN有有效数据时才计算
        ln_nrb = log(PN .* (z.^2));
    else
        ln_nrb = zeros(size(PN));  % 创建零矩阵占位符
    end
    
    % 初始化梯度
    G = zeros(1, Nz);
    
    % 计算梯度
    for zi = 1:(Nz-2)
        if (z(zi+2) - z(zi)) > 0  % 防止除以零
            G(zi+1) = (ln_nrb(zi+2) - ln_nrb(zi)) / (z(zi+2) - z(zi));
        else
            G(zi+1) = 0;  % 如果高度间隔为零，设置梯度为0
        end
    end
    
    % 检测云层
    for ilayer = 1:Nlayer
        % 计算F值
        F1 = 100 * (PN - B) / MA;
        PMX = 0;
        
        for i = 1:base(ilayer)
            PMX = PMX + PM(i);
        end
        
        F = Area(ilayer) * PMX / (z(top(ilayer)) - z(base(ilayer))) / MA;
        F2 = nanmax(F1(base(ilayer):top(ilayer)+1));
        
        % 根据阈值判断云层
        if F > F_threshold || F2 > F2_threshold
            cloud(itime, base(ilayer):top(ilayer)+1) = 1;
        end
        
        % 检测2km以下的水云 (与Python版本一致)
        if (z(base(ilayer)) < 2) && (nanmedian(PM(base(ilayer):top(ilayer)+1)) > 0.1) && (prctile(PM(base(ilayer):top(ilayer)+1), 90) > 1)
            if F > 1
                cloud(itime, base(ilayer):top(ilayer)+1) = 1;
            end
            if nanmax(G(base(ilayer)+1:top(ilayer)+1)) > 3 || nanmin(G(base(ilayer)+1:top(ilayer)+1)) < -7
                cloud(itime, base(ilayer):top(ilayer)+1) = 1;
            end
        end
    end